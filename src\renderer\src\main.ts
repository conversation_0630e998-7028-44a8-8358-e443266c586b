import { createApp } from 'vue'
import './style.css'
import App from './App.vue'
import route from "./router";
import AppYYJ from './App_shishahai_yinyuejie.vue'
import routeYYJ from "./router_shishahai_yinyuejie";
import store from '@/store'
import ElementPlus from 'element-plus'
import {ElMessage} from 'element-plus'
import 'element-plus/dist/index.css'
import '@/assets/global.css'
//屏幕适配,傻逼行为，接手项目，无解
import { adapterDraft } from 'adapter-draft'
adapterDraft(1440)

import axios from 'axios'

async function init() {
    
    let config = {}
    try{
        config = await window.api.getConfig()

    }catch (err){
        // ElMessage.error('读取配置文件失败')
    }
    

    let baseUrl = import.meta.env.VITE_BASE_URL
    window.env = process.env.NODE_ENV
    let ignoreURL = [
        '/api/versions', //检查更新服务
    ]
    // adapterDraft(window.env === 'development' ? 6000 : 1440)
    axios.defaults.baseURL = baseUrl;
    axios.interceptors.request.use(
        config => {
            // 从本地存储中获取token
            const token = window.token;
            // console.log(token)
            // 如果存在token，则将其添加到请求头中
            if (token) {
                config.headers.Authorization = `Bearer ${token}`;
            }
            return config;
        },
        error => {
            // 处理请求错误
            console.error('请求拦截器错误:', error);
            return Promise.reject(error);
        }
    )
    axios.interceptors.response.use(
        response => {
            // 如果返回的code不为0，则将其转为错误处理
            if (response.data && response.data.code !== 0) {
                // ElMessage({
                //     message: response.data.msg,
                //     type: 'error',
                // });
                console.log(response.config.url)
                let hasIgnore = ignoreURL.find(e=>{
                    return response.config.url.includes(e)
                })
                if (hasIgnore) return response
                return Promise.reject(response.data);
            }
            return response;
        },
        error => {
            return Promise.reject(error);
        }
    );

    console.log(config)
    if (config.APP?.style == 'yyj') {
        let app =  createApp(AppYYJ)
        app.use(store);
        app.use(routeYYJ);
        app.use(ElementPlus)
        app.mount('#app')
        return
    }
    
    // import 'vant/lib/index.css';
    let app =  createApp(App)
    app.use(store);
    app.use(route);
    app.use(ElementPlus)
    app.mount('#app')
}

init()
