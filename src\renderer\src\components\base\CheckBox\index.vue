<template>
  <div class="my-checkbox">
    <div
      class="my-checkbox__box"
      :class="{ 'my-checkbox__box--active': showDialog }"
      @click.stop="showDialog = !showDialog"
    ></div>
    <slot></slot>
  </div>
</template>

<script setup>
 import {ref} from 'vue'
 const showDialog = defineModel({ default: false })
 defineOptions({
  inheritAttrs: false
 })
</script>

<style lang="less" scoped>
@checkBox:rgba(250, 141, 229, 1);
@outerColor:linear-gradient( rgba(179, 252, 204, 1) , rgba(242, 131, 209, 1));
  
.my-checkbox {
  display: flex;
  align-items: center;
  cursor: pointer;
  gap: 10rem;

  &__box {
     width: 50rem;
     height: 50rem;
     position: relative;
     margin-right: 10rem;
     transition: all 0.2s;
     box-sizing: border-box;
     overflow: hidden; // 新增overflow
    background: url('@/assets/welcome/images/check.svg') no-repeat center center;
    background-size: cover;
    &::after {
      content: '';
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%) scale(0);
      width: 26rem;
      height: 26rem;
      background: @checkBox;
      border-radius: 50%;
      transition: all 0.2s;
      z-index: 1;
    }
    &--active {
      border-color: @checkBox;
      &::after {
        transform: translate(-50%, -50%) scale(1);
      }
    }
  }
}
</style>
