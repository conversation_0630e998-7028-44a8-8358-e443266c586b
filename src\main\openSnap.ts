import { spawn, exec } from "child_process"; 
import { Key, keyboard } from '@nut-tree-fork/nut-js'

// 启动 Snap Camera 并最小化到任务栏
const child = spawn(
    '"C:\\Program Files\\Snap Inc\\Snap Camera\\Snap Camera.exe"',
    [],
    {
        detached: true,
        windowsHide: true, // 隐藏窗口
        shell: true,       // 允许带引号的路径
    }
);

child.stdout?.on("data", (e) => {
    console.log(e.toString());
});
setTimeout(e=>{
    keyboard.type(Key.LeftControl, Key.Z)
},3000)

let kill = () => {
    // 先尝试关闭子进程
    child.kill();
    // 强制关闭 Snap Camera 主进程
    exec('taskkill /IM "Snap Camera.exe" /F', (err, stdout, stderr) => {
        if (err) {
            console.error("关闭 Snap Camera 失败:", err);
        }
    });
};

export {kill}