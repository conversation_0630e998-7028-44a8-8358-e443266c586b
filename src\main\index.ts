import { app, shell, BrowserWindow, ipcMain, session } from 'electron'
import { join } from 'path'
import { electronApp, optimizer, is } from '@electron-toolkit/utils'
import icon from '../../resources/app.ico?asset'
// import udpService from './services/udpService'
import httpService from './services/httpService' // 导入 HTTP 服务
import fs from 'fs'
import path from 'path'
import url from 'url'
import { Key, keyboard } from '@nut-tree-fork/nut-js'
// 删除重复的导入
// import { app, ipcMain } from 'electron'
import machineId from 'node-machine-id'
import {kill} from './openSnap.ts'

// 根据构建模式设置不同的应用标识，确保test和prod版本可以同时运行
const buildMode = import.meta.env.MODE || 'prod';

// 为不同版本设置不同的应用名称，这样它们就会被视为不同的应用
// 必须在导入readConfig之前设置，这样userData路径才会正确
if (buildMode === 'test') {
  app.setName('popofifi-test');
} else {
  app.setName('popofifi-prod');
}

// 现在导入config，此时app.setName()已经生效
import getConfig from './readConfig'
import {setLocalConfig} from './readConfig'
import { getVersions, getCurrentVersion } from './getVersion'
const machineID = machineId.machineIdSync(true)

// 现在请求单实例锁，由于应用名称不同，test和prod版本不会互相冲突
const gotTheLock = app.requestSingleInstanceLock();

app.commandLine.appendSwitch('ignore-certificate-errors')
let isTest = import.meta.env.MODE === 'test'
let isDev = process.env.NODE_ENV === 'development'
if (!gotTheLock) {
  console.log('应用已在运行，禁止多开！');
  app.quit(); // 退出当前进程
} 

function createWindow(): void {
  // 获取配置
  const config = getConfig();

  // Create the browser window.
  const mainWindow = new BrowserWindow({
    width: 500,
    height: 948,
    x: isDev ? 200 : 0,
    y: 0,
    frame: false, // 隐藏默认标题栏
    show: false,
    autoHideMenuBar: true,
    alwaysOnTop: (config.APP.alwaysOnTop !== undefined)? config.APP.alwaysOnTop : !isDev,
    fullscreen: (config.APP.fullScreen !== undefined)? config.APP.fullScreen : (!isDev || isTest),
    ...(process.platform === 'linux' ? { icon } : {}),
    icon,
    webPreferences: {
      allowRunningInsecureContent: true,
      preload: join(__dirname, '../preload/index.js'),
      devTools: (isDev || isTest),
      sandbox: false,
      nodeIntegration: true,
    }
  })

  mainWindow.webContents.openDevTools()
  mainWindow.on('ready-to-show', () => {
    mainWindow.show()
  })
  // 窗口创建后扫描音频文件并发送到渲染进程
  mainWindow.webContents.on('did-finish-load', () => {})
  mainWindow.webContents.setWindowOpenHandler((details) => {
    shell.openExternal(details.url)
    return { action: 'deny' }
  })

  // HMR for renderer base on electron-vite cli.
  // Load the remote URL for development or the local html file for production.
  if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
    mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL'])
  } else {
    mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
  }
}

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.whenReady().then(() => {
  const { session } = require('electron')
  session.defaultSession.setPermissionRequestHandler((webContents, permission, callback) => {
    if (permission === 'media') {
      callback(true) // 允许媒体设备访问
    } else {
      callback(false)
    }
  })

  // Set app user model id for windows
  electronApp.setAppUserModelId('com.electron')

  // udpService.initUdpServices()
  httpService.initHttpService() // 初始化 HTTP 服务

  // ipcMain.handle('send_udp_message', async (_, { message }) => {
  //   try {
  //     const result = await udpService.sendUdpMessage(message)
  //     return result
  //   } catch (error) {
  //     console.error('UDP message handler error:', error)
  //     throw error
  //   }
  // })

  ipcMain.handle('kuaijiejian', async (_, message) => {
    try {
      console.log('message', message)
      if (message.name == 'huanlianopen'){
        await keyboard.type(Key.LeftControl, Key.Z);
      }else if (message.name == 'huanlianclose'){
        await keyboard.type(Key.LeftControl, Key.Z);
      }
    } catch (error) {
      console.error('UDP message handler error:', error)
      throw error
    }
  })

  ipcMain.handle('get-machine-info', async () => {
    console.log(machineID)
    return {
      machineId: machineID
    }
  })
  ipcMain.handle('get-app-path', () => {
     console.log(path.dirname(app.getPath('exe')), 'dsdsdsd')
    return path.dirname(app.getPath('exe'))
  })

  // 在app.whenReady().then()中添加：
  ipcMain.handle('get-app-dev', () => {
    return process.env.NODE_ENV;
  });

  ipcMain.handle('get-config', () => {
    return getConfig()
  });

  ipcMain.handle('set-local-config', (_, key, value) => {
    console.log(key)
    console.log(value)
    setLocalConfig(key,value)
  });

  // 获取版本信息
  ipcMain.handle('get-version', () => {
    return getCurrentVersion();
  });

  // 获取完整版本信息数组
  ipcMain.handle('get-version-info', () => {
    return getVersions();
  });

  // 重启应用
  ipcMain.handle('restart-app', () => {
    app.relaunch();
    app.exit(0);
  });




  // Default open or close DevTools by F12 in development
  // and ignore CommandOrControl + R in production.
  // see https://github.com/alex8088/electron-toolkit/tree/master/packages/utils
  app.on('browser-window-created', (_, window) => {
    optimizer.watchWindowShortcuts(window)
  })
  // 在主进程代码中添加

  // IPC test
  ipcMain.on('ping', () => console.log('pong'))

  createWindow()

  app.on('activate', function () {
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })

})

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// In this file you can include the rest of your app's specific main process
// code. You can also put them in separate files and require them here.
// 添加关闭应用程序的处理程序
ipcMain.on('close-app', () => {
  app.quit()
})
// 在应用退出前关闭 HTTP 服务
app.on('will-quit', () => {
  kill()
  httpService.closeHttpService()
})
