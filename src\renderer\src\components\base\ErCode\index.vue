<template>
  <div class="base-ercode">
    <template v-if="!myProps.isPaySuccess">
    <img class="my-img" :style="{
      width: myProps.imgWidth,
      height: myProps.imgHeight,
    }" 
     :src="qrImgUrl"
     alt="二维码" 
     />
     </template>
     <template v-else>
        <img 
        class="my-img" 
        src="@/assets/9_9/images/pay-success.png" 
        :style="{
      width: myProps.imgWidth,
      height: myProps.imgHeight,
    }" 
        >
     </template>
  </div>
</template>

<script setup>
import QRCode from 'qrcode'
import {ref,onMounted} from 'vue'
const myProps = defineProps({
    codeData:{
        type:String,
        default:''
    },
    CodeOptions:{
        type:Object,
        default:()=>{
            return {
                margin: 1,
                color: { // 二维码颜色
                    dark: '#000000', // 深色
                    light: '#ffffff' // 浅色
                }
            }
        }
    },
    imgWidth:{
        type:String,
        default:'240rem' 
    },
    imgHeight:{
        type:String,
        default:'240rem' 
    },
    isPaySuccess:{
        type:Boolean,
        default:false
    }
})
const qrImgUrl = ref('');

const handleInitGetQrCode = async () => {
    try {
    const imgUrl =  await QRCode.toDataURL(myProps.codeData, myProps.CodeOptions);
     qrImgUrl.value = imgUrl; 

    }
    catch (error) {
        console.error("生成二维码失败", error); 
    }
}

onMounted(()=>{
    handleInitGetQrCode();
})
</script>

<style lang="less" scoped>
.base-ercode{
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    .my-img{
    } 
    .success {

    }
}
</style>