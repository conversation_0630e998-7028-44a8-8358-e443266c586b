# 登录页面更新检查功能

本功能在登录页面集成了自动更新检查，可以检测服务器上的最新版本，并提供手动更新选项。

## 功能特性

### 1. 自动版本检查
- 在登录页面加载后1秒自动检查更新
- 对比当前版本与服务器最新版本
- 显示详细的版本信息对比

### 2. 版本信息显示
- **当前版本**: 显示本地应用版本号、构建时间、Git哈希
- **最新版本**: 显示服务器最新版本号、发布时间、状态
- **版本对比**: 直观显示当前版本与最新版本的差异

### 3. 手动更新选择
- 发现新版本时显示更新提示
- 用户可选择"立即更新"或"跳过"
- 支持下载进度显示
- 更新完成后自动重启应用

### 4. 错误处理
- 网络连接失败时显示错误信息
- 提供重试按钮
- 服务器无响应时的友好提示

## 使用方法

### 1. 启动应用
```bash
npm run dev    # 开发模式
npm run test   # 测试模式  
npm run prod   # 生产模式
```

### 2. 查看更新检查
1. 应用启动后会自动跳转到登录页面
2. 等待1秒后，更新检查器会自动显示
3. 系统会自动检查服务器上的最新版本

### 3. 处理更新
- **有更新时**: 
  - 显示绿色的"发现新版本！"提示
  - 点击"立即更新"开始下载
  - 下载完成后程序自动重启
- **无更新时**: 
  - 显示绿色的"已是最新版本"提示
- **检查失败时**: 
  - 显示红色错误信息
  - 点击"重试"按钮重新检查

## 配置选项

### UpdateChecker组件参数

```vue
<UpdateChecker 
  :auto-check="true"                    // 是否自动检查更新
  :show-details="true"                  // 是否显示详细信息
  update-server-url="http://localhost:8080"  // 更新服务器地址
/>
```

### 更新服务器配置

更新服务器需要提供以下API接口：

- `GET /api/versions` - 获取版本列表
- `GET /api/versions/{version}` - 获取指定版本信息  
- `GET /api/versions/{version}/download` - 下载版本文件

## API接口说明

### 版本列表接口
```
GET /api/versions
```

响应格式：
```json
{
  "data": [
    {
      "version": "1.0.20",
      "status": "active",
      "created_at": "2025-07-22T14:00:00Z",
      "file_name": "popofifi-1.0.20.exe",
      "file_size": 52428800,
      "description": "修复了一些已知问题，提升了性能"
    }
  ]
}
```

### 版本下载接口
```
GET /api/versions/{version}/download
```

返回二进制文件流，支持断点续传。

## 文件结构

```
src/
├── main/
│   └── getVersion.ts              # 版本管理模块
├── preload/
│   └── index.ts                   # 预加载脚本（包含版本和重启API）
└── renderer/src/
    ├── components/
    │   └── UpdateChecker.vue      # 更新检查组件
    └── views/
        ├── popfifi/
        │   └── login.vue          # 登录页面（集成更新检查）
        └── shishahai_yinyuejie/
            └── login.vue          # 登录页面（集成更新检查）
```

## 版本比较逻辑

系统使用语义化版本号进行比较：
- 主版本号.次版本号.补丁版本号
- 例如：1.0.19 < 1.0.20 < 1.1.0 < 2.0.0

## 安全考虑

1. **HTTPS连接**: 生产环境建议使用HTTPS连接更新服务器
2. **文件校验**: 建议在下载后验证文件完整性
3. **权限控制**: 更新服务器应实现适当的访问控制
4. **签名验证**: 生产环境建议对更新文件进行数字签名验证

## 故障排除

### 常见问题

1. **更新检查失败**
   - 检查网络连接
   - 确认更新服务器地址正确
   - 查看控制台错误信息

2. **版本信息显示异常**
   - 检查版本管理模块是否正确导入
   - 确认package.json中版本号格式正确

3. **下载失败**
   - 检查服务器文件是否存在
   - 确认网络连接稳定
   - 查看浏览器下载权限设置

4. **重启失败**
   - 检查应用权限
   - 确认主进程重启API正常工作

### 调试方法

1. 打开开发者工具查看控制台日志
2. 检查网络面板中的API请求
3. 查看应用主进程日志输出

## 更新日志

- **v1.0.0**: 初始版本，基本更新检查功能
- **v1.0.1**: 添加下载进度显示
- **v1.0.2**: 优化错误处理和用户体验
