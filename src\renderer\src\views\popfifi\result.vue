<template>
  <div class="result-page">
    <!-- <div class="header">回到纯真年代</div> -->
    <img class="bk" src="@/assets/welcome/images/back.png" />
    <img class="title" src="@/assets/images/addGroup.png" />
    <!-- 左上角按钮 -->
    <div class="button-left-top">
      <button class="back-button" @click="goBack">返回拍照</button>
    </div>

    <!-- 右上角按钮组 -->
    <div class="button-right-top">
      <button class="regen-button" @click="reGenerate" :disabled="isReGenerating">
        <span v-if="isReGenerating">
          <span class="spinner-inline"></span>
          正在生成…
        </span>
        <span v-else>重新生成</span>
      </button>
      <!-- <button class="print-button" @click="openPrint">打印</button> -->
    </div>

    <div class="newMain">
      <div class="center">
        <!-- <img src="@/assets/images/resultmain.png" class="result-img" /> -->
        <img
          class="mainImg"
          v-if="results[tempIndex]?.result_url"
          :src="results[tempIndex].result_url"
          alt="图像展示"
        />
        <img
          class="mainImg mainImgPos"
          style="opacity: 0"
          v-if="results[currentIndex]?.result_url"
          :src="results[currentIndex].result_url"
          alt="图像展示"
        />
      </div>
      
      <div class="thumbnail-wrapper">
        <qrcode
          :results="results"
          :currentIndex="currentIndex"
          :subjectFile="subjectFile"
          :gender="gender"
          ref="qrcode"
          class="qrcodeBox"
        ></qrcode>
        <img class="userLogo" src="@/assets/images/userLstLogo.png">
        <img class="bg" src="@/assets/images/userLstBk.png">
        <div class="userBox">
          <div
            v-for="(item, index) of imgg"
            :key="index"
            :class="`itembox boxx${index}`"
            @click="switchImage(index)"
          >
            <img
              :class="'hh2 imgg' + index"
              v-if="results[index]?.result_url"
              :src="results[index].result_url"
            />
            <div class="loadingBox" v-if="!results[index]?.result_url">
              <img class="loading" src="@/assets/images/loading.png" />
            </div>
          </div>
        </div>
        
        <!-- <button class="scroll-btn left" @click="scrollThumbnails(-1)">‹</button>
        <div class="thumbnail-list" ref="thumbnailList">
          <div
            v-for="(item, index) in imgg"
            :key="item.style"
            class="thumb"
            :class="{ active: currentIndex === index }"
            @click="switchImage(index)"
          >
            <img :src="results[index]?.result_url" v-if="results[index]?.result_oss" />
            <div class="loadingBox" v-if="!results[index]?.result_oss">
              <img class="loading" src="@/assets/images/loading.png" />
            </div>
          </div>
        </div>
        <button class="scroll-btn right" @click="scrollThumbnails(1)">›</button> -->
      </div>
    </div>
    <!-- 主图展示 -->
    <!-- <div
      class="main-image-container"
      v-if="results.length"
      :style="{ backgroundImage: `url(${bgImages[(currentIndex % 9)]})` }"
    >
      <img :src="results[currentIndex].result_url" alt="图像展示" />
    </div> -->

    <!-- 缩略图列表带左右滚动控制 -->
    <!-- <div class="guess">
      猜你喜欢
      <div></div>
    </div> -->
    

    

    <!-- 打印组件（浮层展示） -->
    <!-- <PrintDialog
      v-if="showPrintDialog"
      :results="results"
      :currentIndex="currentIndex"
      :subjectFile="subjectFile"
      :gender="gender"
      @close="showPrintDialog = false"
      @appendResult="handleAppendResult"
    /> -->
  </div>
</template>

<script>
// import PrintDialog from "./result_PrintDialog.vue";
import axios from 'axios'
import bg1 from '@/assets/home/<USER>'
import bg2 from '@/assets/home/<USER>'
import bg3 from '@/assets/home/<USER>'
import bg4 from '@/assets/home/<USER>'
import bg5 from '@/assets/home/<USER>'
import bg6 from '@/assets/home/<USER>'
import bg7 from '@/assets/home/<USER>'
import bg8 from '@/assets/home/<USER>'
import bg9 from '@/assets/home/<USER>'
import makeImg from './result_makeImg.vue'
import {
  animate,
  createTimeline,
  createTimer
  // ...other methods
} from 'animejs'
import qrcode from './result_qrcode.vue'
import { nextTick } from 'vue'
export default {
  name: 'ResultPage',
  components: {
    makeImg,
    qrcode
  },
  data() {
    return {
      results: [],
      currentIndex: 0,
      tempIndex: 0,
      subjectFile: null,
      gender: 'female',
      remainCodes: [],
      isGenerating: false,
      cancelFlag: false,
      templateQueue: [],
      isReGenerating: false,
      showPrintDialog: false,

      lst: [],
      taskId: '',
      avatarId: '',
      timeout: null,
      aniTimeout: null,
      reInterval: null,
      bgImages: [bg1, bg2, bg3, bg4, bg5, bg6, bg7, bg8, bg9],
      state: history.state,
      selectItem: {},
      imgg: [
        { bk: '@/assets/home/<USER>', pos: [1, 53], ro: [-26], bg: 1 },
        { bk: '@/assets/home/<USER>', pos: [4, -4], ro: [-22], bg: 6 },
        { bk: '@/assets/home/<USER>', pos: [44, -35], ro: [15], bg: 4 },
        { bk: '@/assets/home/<USER>', pos: [80, -7], ro: [17], bg: 3 },
        { bk: '@/assets/home/<USER>', pos: [81, 54], ro: [9], bg: 7 }
      ],
      showPopInterval: null
    }
  },
  mounted() {
    const state = history.state
    if (!state?.results) {
      console.error('没有结果数据')
      this.results = [
    {
        "subtask_id": "01980817-6fcc-79b2-ae74-bd4fcfe86aff",
        "style": "p_113",
        "result_oss": "tmp/20250714/TEMPAA4C4B72A51D794AA8B8F11DEDB5/SBAHCTEQS3G0SKKB.png",
        "result_url": "https://qcard-test.oss-cn-beijing.aliyuncs.com/tmp%2F20250714%2FTEMPAA4C4B72A51D794AA8B8F11DEDB5%2FSBAHCTEQS3G0SKKB.png?Expires=1752568844&OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&Signature=yKl8pVJcWsh3w6HtVx%2F2%2BW7Sdi4%3D",
        "status": 2
    },
    {
        "subtask_id": "01980817-6fcc-79b3-a844-ed94d9ef0e20",
        "style": "p_003",
        "result_oss": "tmp/20250714/TEMPAA4C4B72A51D794AA8B8F11DEDB5/53MRAQ43YIRA35XZ.png",
        "result_url": "https://qcard-test.oss-cn-beijing.aliyuncs.com/tmp%2F20250714%2FTEMPAA4C4B72A51D794AA8B8F11DEDB5%2F53MRAQ43YIRA35XZ.png?Expires=1752568844&OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&Signature=dm5f3KCdujsHpoP%2FMXrjq1nD1hI%3D",
        "status": 2
    },
    {
        "subtask_id": "01980817-6fcc-79b4-b9ad-4b592d4b32ac",
        "style": "p_133",
        "result_oss": "tmp/20250714/TEMPAA4C4B72A51D794AA8B8F11DEDB5/A05899O0DNC9DY08.png",
        "result_url": "https://qcard-test.oss-cn-beijing.aliyuncs.com/tmp%2F20250714%2FTEMPAA4C4B72A51D794AA8B8F11DEDB5%2FA05899O0DNC9DY08.png?Expires=1752568844&OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&Signature=Ab1lD0u3Dr5pkl77wU0CW6MMLeQ%3D",
        "status": 2
    },
    {
        "subtask_id": "01980817-6fcc-79b5-afc7-89c8ed1d216e",
        "style": "p_135",
        "result_oss": "tmp/20250714/TEMPAA4C4B72A51D794AA8B8F11DEDB5/QT43TWNNQ1SWNUAA.png",
        "result_url": "https://qcard-test.oss-cn-beijing.aliyuncs.com/tmp%2F20250714%2FTEMPAA4C4B72A51D794AA8B8F11DEDB5%2FQT43TWNNQ1SWNUAA.png?Expires=1752568844&OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&Signature=oPvbXb3EwqzgpZILMMcQK%2B%2Bncg4%3D",
        "status": 2
    },
    {
        "subtask_id": "01980817-6fcc-79b6-b344-fc8dcdd446e2",
        "style": "p_005",
        "result_oss": "",
        "result_url": "",
        "status": 0
    }
]
      console.log(this.state)
      console.log(this.results)
      return
    }
    console.log('result')
    this.results = []
    this.lst = state.remainCodes
    // this.addImg(state.results)
    this.subjectFile = state.subjectFile
    this.gender = state.gender || 'female'
    this.remainCodes = state.remainCodes || []
    this.taskId = state.taskId
    this.avatarId = state.avatarId

    // const existingCodes = this.results.map(r => r.code);
    // this.templateQueue = this.remainCodes.filter(code => !existingCodes.includes(code));
    this.results = this.remainCodes.map((code) => (code = {}))
    this.results[state.resultsId] = state.results
    this.getOthersImage()
  },
  unmounted() {
    this.timeout && clearTimeout(this.timeout)
    this.aniTimeout && clearTimeout(this.aniTimeout)
    this.reInterval && clearInterval(this.reInterval)
    axios.post('/api/v1.0/digital-avatar/cancel_gen_2d', {
      client_id: window.clientId.screen_num,
      digital_avatar_id: this.avatarId,
      task_id: this.taskId
    })
  },
  methods: {
    addImg(item) {
      let code = item.style.slice(2)
      item.code = code
      let index = this.results.findIndex((e) => e.code == code)
      if (index != -1) {
        this.results.splice(index, 1, item)
        return
      }
      if (this.lst.includes(code)) {
        this.results.push(item)
      }
    },
    getOthersImage() {
      let getImg = (e) => {
        // if (this.lst.length <= this.results.length){
        //   clearInterval(this.interval)
        //   console.info('最后一张：',new Date().getTime())
        //   return
        // }
        axios
          .get('/api/v1.0/digital-avatar/task/2d/get', {
            params: {
              task_id: this.taskId,
              client_id: window.clientId.screen_num
            }
          })
          .then((e) => {
            let lst = e.data.data.avatars[0].styles
            this.results.map((e, index) => {
              if (e.result_oss) return
              this.results[index] = lst[index]
            })
            // this.results = lst
            console.log(lst)
            if (!this.results.find((e) => !(e?.status == 2))) {
              clearTimeout(this.timeout)
              // console.info('最后一张：',new Date().getTime())
              return
            } else {
              this.timeout = setTimeout((e) => {
                getImg()
              }, 1000)
            }

            // this.addImg(e.data.data)
            // console.info('一张：',new Date().getTime())
          })
      }
      getImg()
      // this.interval = setInterval(()=>{
      //   getImg()
      // },1000)
    },
    /////
    getBgImage(index) {
      const bgIndex = (index % 9) + 1
      let url = new URL(`@/assets/home/<USER>
      console.log(url)
      return url
    },

    openPrint() {
      this.showPrintDialog = true
    },

    handleAppendResult(newResult) {
      this.results.push(newResult)
    },

    switchImage(index) {
      if (this.isReGenerating) return
      console.log(index)
      if (index == this.currentIndex) {
        return
      }
      if (!this.results[index]) return
      this.currentIndex = index
      this.selectItem = this.results[index]
      if (this.selectItem.status != 2)return
      // 动画
      let box = document.getElementsByClassName('newMain')[0]
      let removePic = (e) => {
        let pics = box.getElementsByClassName('tempNodeX')
        Array(...pics).forEach((e) => {
          box.removeChild(e)
        })
      }
      // removePic()
      let source = document.getElementsByClassName(`boxx${index}`)[0]
      let newNode = source.cloneNode(true)
      newNode.style.cssText = source.style.cssText
      const rect = source.getBoundingClientRect();
    
      // 设置绝对定位样式
      newNode.style.position = 'absolute';
      newNode.style.left = rect.left + 'px';
      newNode.style.top = rect.top + 'px';
      newNode.style.width = rect.width + 'px';
      newNode.style.height = rect.height + 'px';
      newNode.classList.add('tempNodeX')
      // let bk = newNode.getElementsByClassName('hh1')
      // newNode.removeChild(bk[0])
      // 将克隆节点添加到 body 中进行动画
      box.appendChild(newNode)
      // 等待current 渲染完成
      setTimeout(() => {
        let rect = document.getElementsByClassName('mainImgPos')[0].getBoundingClientRect()
        let node = document.getElementsByClassName(`mainImg`)[0]
        console.log(rect)
        animate(node, {
          keyframes: {
            '0%': { opacity: 1 },
            '10%': { opacity: 0 },
            '100%': { opacity: 0 }
          }
        })
        animate(newNode, {
          duration: 500,
          translateX: '-50%',
          height: rect.height + 'px',
          width: rect.width + 'px',
          left: '50%',
          top: rect.top + 'px',
          padding: 0,
          // marginTop: '20vh',
          onComplete: () => {
            this.tempIndex = index

            // node.style.opacity = '0.5'
            this.aniTimeout = setTimeout(() => {
              box.removeChild(newNode)
              node.style.opacity = '1'
              // box.removeChild(newNode);
            }, 1000)
          }
        })
      }, 50)
    },

    scrollThumbnails(direction) {
      // if (direction>0){
      //   if (this.currentIndex == this.results.length-1){
      //     return
      //   }
      //   this.currentIndex++
      //   this.selectItem = this.results[this.currentIndex]
      // }else{
      //   if (this.currentIndex == 0){
      //     return
      //   }
      //   this.currentIndex--
      //   this.selectItem = this.results[this.currentIndex]
      // }
      const container = this.$refs.thumbnailList
      if (container) {
        const scrollAmount = container.clientWidth / 2
        container.scrollBy({
          left: direction * scrollAmount,
          behavior: 'smooth'
        })
      }
    },

    goBack() {
      this.$router.push({ path: '/welcome' })
    },

    async reGenerate() {
      let current = this.results[this.currentIndex]
      const code = current.code

      let that = this
      let state = history.state
      // let taskId= new Date().getTime().toString()
      axios
        .post('/api/v1.0/digital-avatar/re_gen_2d_style', {
          client_id: window.clientId.screen_num,
          task_id: this.taskId,
          digital_avatar_id: state.avatarId,
          styles: [current.style]
          // "avatar_image": state.objectKey
        })
        .then((e) => {
          this.isReGenerating = true // ✅ 设置按钮状态

          this.cancelFlag = true
          // let taskId = e.data.data.task_id
          that.reInterval = setInterval(() => {
            axios
              .get('/api/v1.0/digital-avatar/task/2d/get', {
                params: {
                  task_id: this.taskId,
                  client_id: window.clientId.screen_num
                }
              })
              .then((e) => {
                let lst = e.data.data.avatars[0].styles
                // this.results.map((e,index)=>{
                //   if (e.result_oss)return
                //   this.results[index] = lst[index]
                // })
                // this.results = lst
                let item = lst.find((e) => e.style == current.style)
                console.log(lst)
                if (item.status == 2) {
                  let index = that.results.findIndex((e) => e.style == item.style)
                  that.results[index] = item
                  this.isReGenerating = false
                  clearInterval(that.reInterval)
                  // console.info('最后一张：',new Date().getTime())
                  return
                } else if (item.status == 4) {
                  this.isReGenerating = false
                  clearInterval(that.reInterval)
                  return
                }
              })
          }, 1000)
        })
      return
    }
  }
}
</script>

<style scoped lang="less">
.loadingBox {
  height: 100%;
  display: flex;
  width: 16vw;
  justify-content: center;
  align-items: center;
  .loading {
    height: 150rem !important;
    width: 150rem !important;
    /* 添加旋转动画 */
    animation: spin 2s linear infinite;
  }
}
/* 定义旋转动画 */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.newMain {
  // margin-top: 30vh;
  height: 100vh;
  width: 100vw;
  position: relative;
  .center {
    position: relative;
    margin-top: 20vh;
    width: 100vw;
    height: 45vh;
    .result-img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    .mainImg {
      height: 86%;
      // width: 15%;
      position: absolute;
      top: 10%;
      transform: translateX(-50%);
      left: 50%;
      z-index: 1;
    }
  }
  .itembox {
    // position: absolute;
    height: 380rem;
    width: 240rem;
    padding: 10rem;
    overflow: hidden;
  }
  .hh1,
  .hh2 {
    height: 100%;
    width: 100%;
    object-fit: contain;
    // position: absolute;
    top: 0;
    left: 0;
  }
}
.bk {
  height: 100vh;
  width: 100vw;
  position: absolute;
  object-fit: cover;
  z-index: -1;
}
.title {
  // height: 200rem;
  width: 60vw;
  position: absolute;
  z-index: 555;
  margin-top: 100rem;
}
.result-page {
  width: 100vw;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  // background: #000;
  position: relative; // 关键：用于内部绝对定位按钮

  .header {
    font-size: 5vw;
    color: #fff;
    margin-top: 3vh;
  }

  .main-image-container {
    margin-top: 30vh;
    width: 60vw;
    height: 100vw;
    border-radius: 12px;
    overflow: hidden;
    background-color: #000;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    display: flex;
    justify-content: center;
    align-items: center;

    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
      display: block;
      z-index: 1;
    }
  }

  .guess {
    text-align: left;
    width: 100vw;
    font-size: 50rem;
    color: #ffe600;
    position: relative;
    padding: 20rem;
    text-indent: 50rem;
    div {
      position: absolute;
      height: 5rem;
      width: 100vw;
      bottom: 0;
      background-color: #cecece;
    }
  }
  .thumbnail-wrapper {
    // margin-top: 2vh;
    display: flex;
    align-items: center;
    width: 94vw;
    // overflow: hidden;
    // margin-top: 1vh;
    position: absolute;
    bottom: 1.5vh;
    left: 3vw;
    .qrcodeBox{
      position: absolute;
      bottom: calc(100% - 30rem);
    }
    .userBox{
      height: 100%;
      width: 86%;
      left: 7%;
      top: 50%;
      transform:translateY(-50%);
      display: flex;
      flex-direction: row;
      position: absolute;
      align-items: center;
    }
    .userLogo{
      position: absolute;
      top: 9%;
      left: 50%;
      transform: translateX(-50%);
      width: 25%;
      
    }
    .bg{
      width: 100%;

    }
    .scroll-btn {
      background: rgba(255, 255, 255, 0.1);
      color: #fff;
      border: none;
      font-size: 6vw;
      padding: 0 2vw;
      cursor: pointer;
      z-index: 2;
      height: 100%;
      transition: background 0.2s;
      border-radius: 6px;

      &.left {
        margin-right: 1vw;
      }

      &.right {
        margin-left: 1vw;
      }

      &:hover {
        background: rgba(255, 255, 255, 0.3);
      }
    }

    .thumbnail-list {
      display: flex;
      flex-wrap: nowrap;
      overflow-x: auto;
      scroll-behavior: smooth;
      gap: 2vw;
      width: 100%;
      background-color: #00000080;
      height: 16vh;

      .thumb {
        flex: 0 0 auto;
        border: 2px solid transparent;
        border-radius: 8px;
        cursor: pointer;
        display: flex;
        &.active {
          border-color: #ff7875;
        }

        img {
          // height: 12vh;
          width: 16vw;
          border-radius: 6px;
          object-fit: contain;
        }
      }
    }
  }

  .button-left-top {
    position: absolute;
    top: 16vh;
    left: 2vw;
    z-index: 1002;
    .back-button {
      padding: 10px 20px;
      font-size: 4vw;
      // font-weight: 500;
      // border-radius: 20px;
      color: white;
      background: none;
      border: none;
      // background: linear-gradient(to bottom right, #886bde, #8969df);
      // border: 2px solid rgba(255, 255, 255, 0.75);
      // backdrop-filter: blur(4px);
      // -webkit-backdrop-filter: blur(4px);

    }
  }

  // 右上角按钮组（重新生成 + 打印）
  .button-right-top {
    width: 28vw;
    position: absolute;
    top: 16vh;
    right: 0vw;
    display: flex;
    flex-direction: column;
    gap: 1vh;
    z-index: 1000;
    button {
      padding: 10px 0;
      font-size: 4vw;
      // font-weight: bold;
      text-align: center;
      color: white;
      border: none;
      // box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
      transition:
        background 0.3s,
        transform 0.2s;

      &:hover {
        transform: translateY(-2px);
      }
    }

    .regen-button {
      background: none; //linear-gradient(to right, #36d1dc, #5b86e5);


      &:disabled {
        // background: #999;
        cursor: not-allowed;
        box-shadow: none;
        transform: none;
      }
    }

    .print-button {
      background: linear-gradient(to right, #f7971e, #ffd200);

      &:hover {
        background: linear-gradient(to right, #ffd200, #f7971e);
      }
    }

    .spinner-inline {
      display: inline-block;
      width: 1em;
      height: 1em;
      margin-right: 0.5em;
      border: 2px solid rgba(255, 255, 255, 0.3);
      border-top-color: #fff;
      border-radius: 50%;
      animation: spin 0.8s linear infinite;
      vertical-align: middle;
    }

    @keyframes spin {
      to {
        transform: rotate(360deg);
      }
    }
  }
}
</style>
