<template>
  <div class="home" @click="toSelect">
    <img class="title" src="@/assets/images/popofifititle.png">
    <img class="title2" src="@/assets/images/59.png">
    <img class="bk" src="@/assets/images/background.png">
    <video src="@/assets/video/home2.mp4" autoplay loop muted></video>
    <img class="dianwo" src="@/assets/images/dianwo.png" />
  </div>
</template>

<script>
import homeVideo from '@/assets/video/home.mp4'
console.log(homeVideo)
export default {
  name: 'HomePage',
  data() {
    return {
      homeVideo: homeVideo
    }
  },
  methods: {
    toSelect() {
      this.$router.push({ path: '/rebuild2-capture' })
    }
  },
}
</script>

<style lang="less" scoped>
.title2{
  text-align: center;
  width: 65vw;
  top: 10vh;
  position:absolute;
  z-index: 555;
}
.title{
  // height: 200rem
  width: 90vw;
  position:absolute;
  z-index: 555;
  margin-top: 100rem;
}
.bk{
  height: 100vh;
  width: 100vw;
  position: absolute;
  object-fit: cover;
  z-index: -1;
}
.home {
  height: 100vh;
  width: 100vw;
  video{
    height: 100vh;
    width: 100vw;
    object-fit: contain;
  }
  .dianwo{
    width: 45vw;
    height: 45vw;
    position: absolute;
    right: 10%;
    bottom: 20%;
  }
}
.home {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;

  .background-swiper {
    position: absolute;
    inset: 0;
    z-index: 0;

    .swiper-slide {
      width: 100%;
      height: 100%;

      .bg-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        filter: blur(3px) brightness(0.65);
      }
    }
  }

  .background-overlay {
    position: absolute;
    inset: 0;
    background-color: rgba(0.6, 0.6, 0.6, 0.2);
    z-index: 1;
  }

  .top-text {
    flex: 2;
    z-index: 2;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .title-img {
      width: 75vw;
      margin-bottom: 0.0vh;
    }

    .subtitle-img {
      width: 70vw;
      margin-bottom: 1.5vh;
      background-color: white; // 添加白色背景
      padding: 0.8vh 1.5vw;       // 可选：添加内边距让文字不贴边
      border-radius: 8px;       // 可选：圆角更美观
    }

  }

  .carousel-container {
    flex: 5;
    z-index: 2;
    display: flex;
    justify-content: center;
    align-items: center;

    .carousel-card {
      background: white;
      padding: 9px;
      border-radius: 20px;
      box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
      width: 65vw;

      .carousel-image {
        width: 100%;
        border-radius: 10px;
        object-fit: cover;
        display: block;
      }
    }
  }

  .bottom-button {
    flex: 2;
    position: relative;
    width: 72vw;
    aspect-ratio: 720 / 180;
    margin-bottom: 3vh;
    z-index: 2;
    display: flex;
    justify-content: center;
    align-items: center;

    .button-bg {
      position: absolute;
      width: 100%;
      height: 100%;
      object-fit: contain;
      top: 0;
      left: 0;
    }

    .button-center {
      position: relative;
      width: fit-content;
      margin: 0 auto;

      .button-text-img {
        width: 46vw;
        display: block;
      }

      .star,
      .camera-icon {
        position: absolute;
        width: 5vw;
        height: auto;
        object-fit: contain;
      }

      .star-tl {
        top: -33%;
        left: 6%;
        transform: scale(0.5);
      }

      .star-tr {
        top: 46%;
        right: -2%;
        transform: scale(0.5);
      }

      .star-bl {
        bottom: 38%;
        left: 2%;
        transform: scale(0.3);
      }

      .star-br {
        bottom: -37%;
        right: 2%;
        transform: scale(0.3);
      }

      .camera-icon {
        bottom: 23%;
        right: -13%;
        width: 6.5vw;
      }
    }
  }
}
</style>
