<template>
    <div class="bb">
        <div class="box">
            <canvas id="canvas1" width="1240" height="1748"></canvas>
            <canvas id="canvas2" width="1240" height="1748"></canvas>
            <canvas id="tempcanvas1" width="667" height="1252"></canvas>
            <canvas id="tempcanvas2" width="667" height="1252"></canvas>
        </div>
        
    </div>
</template>
<script>
//667 1252
import background from '@/assets/theme/background.png';
import longre from '@/assets/theme/longre.png';
import topbk from '@/assets/theme/middle.png';
import testQR from '@/assets/theme/qrcode.jpg';
import { imgListArr } from '@/views/popfifi/gridData.js'
const codes = imgListArr
export default {
    props: ['status', 'now'],
    data() {
        return {
            img: null,
            state: {},
            pops: [
                { code: '001', 'name': '洛基Loki', height: 159 , img: [592, 1024]},
                { code: '002', 'name': '蛙公子', height: 201, img: [592, 888] },
                { code: '003', 'name': '小龙人', height: 173, img: [592, 888] },
                { code: '004', 'name': '露娜Luna', height: 124, img: [592, 1024] },
                { code: '005', 'name': '作战小队', height: 168, img: [592, 1008] },
                { code: '006', 'name': '可可Coco', height: 186, img: [592, 928] },
                { code: '007', 'name': '夜云舒', height: 187, img: [592, 920]},
                { code: '008', 'name': 'Y.A.O.', height: 173 , img: [592, 896]},
                { code: '009', 'name': 'Cyber FiFi', height: 186 , img: [592, 928]},
            ],
        };
    },
    watch: {
        now: {
            handler(newVal, oldVal) {
                if (newVal) {
                    console.log(newVal)
                    // this.init()
                }
            },
            immediate: true
        }
    },
    mounted() {
        console.log('init')
        if (!history.state.results){
            this.state = {"back":"/rebuild2-loading?code=001","current":"/rebuild2-result","forward":null,"replaced":false,"position":15,"scroll":null,"client_id":"client_12345","results":{"task_id":"1747304572434","sub_task_id":"5726AA9109A35A2FBC97888EA4A4BC05:p_001","digital_avatar_id":"5726AA9109A35A2FBC97888EA4A4BC05","style":"p_001","avatar_image":"tmp/20250515/57/26/AA9109A35A2FBC97888EA4A4BC05/TYBR7ODG8XAWFQSN.jpg","avatar_http_url":"https://qcard-test.oss-cn-beijing.aliyuncs.com/tmp%2F20250515%2F57%2F26%******************************%2FTYBR7ODG8XAWFQSN.jpg?Expires=1747390972&OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&Signature=Wwqb35mtmZ5wys1%2FL5Qj268Stvg%3D","callback":"https://popofifi-test.xcastle.net/api/v1.0/digital-avatar/gen_2d_callback","status":"done","result_oss":"tmp/20250515/57/26/AA9109A35A2FBC97888EA4A4BC05/Q8EL4H87720V5ROV.png","result_url":"https://qcard-test.oss-cn-beijing.aliyuncs.com/tmp%2F20250515%2F57%2F26%******************************%2FQ8EL4H87720V5ROV.png?Expires=1747390982&OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&Signature=J5skdhbXBP%2BS6FThIHXVBlh48cc%3D","code":"001"},"taskId":"1747304572434","avatarId":"5726AA9109A35A2FBC97888EA4A4BC05","objectKey":"tmp/20250515/57/26/AA9109A35A2FBC97888EA4A4BC05/TYBR7ODG8XAWFQSN.jpg","remainCodes":["001","003","004","006","007","009"],"code":"001","subjectFile":{},"gender":"female"}
            console.log(this.state)
        }
        let files = this.init()

    },
    methods: {
        async init(uid, qrcodePic) {
            let state = this.state  
            // 临时生成
            uid = '10747'
            let now = {
                code: '009',
                result_url: 'https://qcard-dev.oss-cn-beijing.aliyuncs.com/prod%2FCA%2F06%******************************%2FKVO23Y4L3UHYNCMS.png?Expires=1750571987&OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&Signature=qgPO9xlekpCfihtbWfRHTrphZAE%3D'
            }
            qrcodePic = 'https://qcard-dev.oss-cn-beijing.aliyuncs.com/prod%2FCA%2F06%******************************%2Fmini_program_qrcode.jpg?Expires=1750571822&OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&Signature=d0U6PKJDdPbfTWq0p6W3HaCRKaw%3D'
            
            let selectItem = now
            console.log(selectItem)
            if (!selectItem.code)return

            let bk = await this.loadImage(background);
            let userImg = await this.loadImage(now.result_url);
            let _logre = await this.loadImage(longre);
            let _topbk = await this.loadImage(topbk);
            let qrcode = qrcodePic?await this.loadImage(qrcodePic):await this.loadImage(testQR);
            // if (selectItem.code == '004' || selectItem.code == '007') {
            //     bk = _logre
            // }

            const canvas1 = document.getElementById('tempcanvas1');
            const canvas2 = document.getElementById('tempcanvas2');
            
            const ctx1 = canvas1.getContext('2d');
            const ctx2 = canvas2.getContext('2d');
            ctx1.clearRect(0, 0, canvas1.width, canvas1.height);
            ctx2.clearRect(0, 0, canvas2.width, canvas2.height);
            // ctx1.fillStyle='#FFFFFF'
            // ctx1.fillRect(0,0,1240,1748)
            // ctx2.fillStyle='#FFFFFF'
            // ctx2.fillRect(0,0,1240,1748)
            let item = codes.find(e=>e.code == selectItem.code) //this.pops[Number(selectItem.code)-1];
            ctx1.drawImage(bk, 0, 0, 667, 1252);
            ctx1.drawImage(userImg, 0+(667 - userImg.width) / 2, item.height, userImg.width, userImg.height);
            ctx1.drawImage(_topbk, 0+(667 - 596) / 2, (1252 - 1181) / 2, 596, 1181);

            ctx1.font = "bold 38px serif ";
            ctx1.fillStyle = "#0202f5";
            ctx1.fillText(item.name, 180, 220);
            ctx1.fillText('ID:' + uid, 180, 280);


            ctx2.drawImage(bk, 0, 0, 667, 1252);
            ctx2.save(); // 保存当前状态
            ctx2.scale(-1, 1); // 水平翻转
            ctx2.drawImage(userImg,
                -(667 + userImg.width) / 2, // 翻转后需要使用负值
                item.height,
                userImg.width,
                userImg.height
            );
            ctx2.restore(); // 恢复状态

            // 为qrcode图片添加裁剪和圆角
            const dest_x_qr = 370;      // 二维码在画布上的目标 X 坐标
            const dest_y_qr = 850;      // 二维码在画布上的目标 Y 坐标
            const dest_width_qr = 200;  // 二维码在画布上的目标宽度
            const dest_height_qr = 200; // 二维码在画布上的目标高度
            const radius_qr = 40;       // 圆角半径
            const crop_margin = 0;     // 从原图四周各裁剪掉的像素数

            // 计算源图像中裁剪区域的参数
            // qrcode 是已加载的图像对象
            const sx_qr = crop_margin;
            const sy_qr = crop_margin;
            const sWidth_qr = qrcode.width - 2 * crop_margin;   // 从源图像裁剪的宽度
            const sHeight_qr = qrcode.height - 2 * crop_margin; // 从源图像裁剪的高度

            // 确保裁剪后的尺寸大于0
            if (sWidth_qr > 0 && sHeight_qr > 0) {
                ctx2.save();
                ctx2.beginPath();
                // 创建圆角矩形路径 (使用目标坐标和尺寸)
                ctx2.moveTo(dest_x_qr + radius_qr, dest_y_qr);
                ctx2.arcTo(dest_x_qr + dest_width_qr, dest_y_qr, dest_x_qr + dest_width_qr, dest_y_qr + dest_height_qr, radius_qr);
                ctx2.arcTo(dest_x_qr + dest_width_qr, dest_y_qr + dest_height_qr, dest_x_qr, dest_y_qr + dest_height_qr, radius_qr);
                ctx2.arcTo(dest_x_qr, dest_y_qr + dest_height_qr, dest_x_qr, dest_y_qr, radius_qr);
                ctx2.arcTo(dest_x_qr, dest_y_qr, dest_x_qr + dest_width_qr, dest_y_qr, radius_qr);
                ctx2.closePath();
                ctx2.clip(); // 应用裁剪路径

                // 绘制源图像的裁剪部分到目标区域
                ctx2.drawImage(
                    qrcode,         // 源图像
                    sx_qr,          // 源图像裁剪区域的 x 坐标
                    sy_qr,          // 源图像裁剪区域的 y 坐标
                    sWidth_qr,      // 源图像裁剪区域的宽度
                    sHeight_qr,     // 源图像裁剪区域的高度
                    dest_x_qr,      // 画布上目标区域的 x 坐标
                    dest_y_qr,      // 画布上目标区域的 y 坐标
                    dest_width_qr,  // 画布上目标区域的宽度
                    dest_height_qr  // 画布上目标区域的高度
                );
                ctx2.restore(); // 恢复绘图状态
            } else {
                console.warn("QR code 图像太小，无法按指定边距裁剪，或者尚未正确加载。");
                // 可选：如果裁剪失败，可以绘制原始图像或占位符
                // ctx2.drawImage(qrcode, dest_x_qr, dest_y_qr, dest_width_qr, dest_height_qr);
            }

            // 放置到图片中间
            // 获取canvas1和tempcanvas1
            const canvas1Main = document.getElementById('canvas1');
            const ctxMain = canvas1Main.getContext('2d');
            const tempcanvas1 = document.getElementById('tempcanvas1');

            // 计算tempcanvas1居中canvas1的位置
            const dx = (canvas1Main.width - tempcanvas1.width) / 2;
            const dy = (canvas1Main.height - tempcanvas1.height) / 2;

            // 清空canvas1并绘制tempcanvas1到中心
            // ctxMain.clearRect(0, 0, canvas1Main.width, canvas1Main.height);
            ctxMain.fillStyle = '#FFFFFF00';
            ctxMain.fillRect(0, 0, canvas1Main.width, canvas1Main.height);
            ctxMain.drawImage(tempcanvas1, dx, dy);
            // 放置到图片中间
            // 获取canvas1和tempcanvas1
            const canvas2Main = document.getElementById('canvas2');
            const ctxMain2 = canvas2Main.getContext('2d');
            const tempcanvas2 = document.getElementById('tempcanvas2');

            // 计算tempcanvas1居中canvas1的位置
            const dx2 = (canvas1Main.width - tempcanvas1.width) / 2;
            const dy2 = (canvas1Main.height - tempcanvas1.height) / 2;

            // 清空canvas1并绘制tempcanvas1到中心
            // ctxMain.clearRect(0, 0, canvas1Main.width, canvas1Main.height);
            ctxMain2.fillStyle = '#FFFFFF00';
            ctxMain2.fillRect(0, 0, canvas2Main.width, canvas2Main.height);
            ctxMain2.drawImage(tempcanvas2, dx2, dy2);
            // ctx2.drawImage(pbk, (667-item.img[0])/2, item.height, item.img[0], item.img[1]);
            // 将canvas数据转换为Blob对象
            const canvas1Blob = await new Promise(resolve => canvas1Main.toBlob(resolve, 'image/png', 1));
            const canvas2Blob = await new Promise(resolve => canvas2Main.toBlob(resolve, 'image/png', 1));
            
            // 创建File对象
            const file1 = new File([canvas1Blob], 'image1.png', { type: 'image/png' });
            const file2 = new File([canvas2Blob], 'image2.png', { type: 'image/png' });
            console.log(file1, file2);

            // 自动下载 file1
            const url1 = URL.createObjectURL(file1);
            const a1 = document.createElement('a');
            a1.href = url1;
            a1.download = 'image1.png';
            document.body.appendChild(a1);
            a1.click();
            document.body.removeChild(a1);
            URL.revokeObjectURL(url1);

            // 自动下载 file2
            const url2 = URL.createObjectURL(file2);
            const a2 = document.createElement('a');
            a2.href = url2;
            a2.download = 'image2.png';
            document.body.appendChild(a2);
            a2.click();
            document.body.removeChild(a2);
            URL.revokeObjectURL(url2);

            return [file1, file2];
        },
        async loadImage(url) {
            let base64 = await this.fetchImageAsBase64(url)
            return new Promise((resolve, reject) => {
                let img = new Image();
                img.src = base64;
                img.onload = () => {
                    resolve(img);
                };
            });
        },
        async fetchImageAsBase64(url) {
            const response = await fetch(url);
            const blob = await response.blob();
            return new Promise((resolve) => {
                const reader = new FileReader();
                reader.onloadend = () => resolve(reader.result);
                reader.readAsDataURL(blob);
            });
        },
        generImg() {
            let img = this.img;
        }
    }
}
</script>
<style scoped lang="less">
.bb{
    position: absolute;
    bottom: 0;

    height: 5000rem;
    overflow: hidden;
    width: 100%;
    transform: scale(5);
    transform-origin: top;
}
.box{
    transform: scale(0.2);
    transform-origin: top left;
    // height: 3000px;
    // width: 800px;
    position: absolute;
    top: 0;
    left: 0;
    width: 500vw;
    // overflow: scroll;
}
canvas{
    display: inline-block;
}
</style>