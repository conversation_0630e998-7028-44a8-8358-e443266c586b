let baseUrl = import.meta.env.VITE_BASE_URL
let url = '/api/v1.0/digital-avatar/get_upload_url'
import axios from 'axios'
// 将 sendFile 改写成 Promise 方式
const sendFile = async (file, signData) => {
    console.log(file);
    const response = await fetch(signData.signedUrl, {
        method: 'PUT',
        headers: {
            'Content-Type': '',  // 设置文件类型
        },
        body: file,  // 直接上传整个文件
    });
    return {
        objectKey: signData.objectKey,
        avatarId: signData.avatarId,
    }

    console.log('File uploaded successfully');
};

// 将 uploadImg 改写成 Promise 方式
let uploadImg = async function (currentFile, id, progressBar) {
    console.log(currentFile);
    let upUrl = url + '?ext=' + currentFile.type.split('/')[1]

    if (id) {
        upUrl += '&digital_avatar_id=' + id
    }

    return new Promise((resolve, reject) => {
        axios.get(upUrl)
            .then(data => {
                data = data.data
                let signData = {
                    avatarId: data.data.digital_avatar_id,
                    objectKey: data.data.object_key,
                    signedUrl: data.data.upload_url
                };
                
                return sendFile(currentFile,signData );
            })
            .then(uploadedFileKey => {
                // 假设 showResult 是一个展示结果的函数
                // showResult(uploadedFileKey);
                resolve(uploadedFileKey);
            })
            .catch(error => {
                console.error('上传过程出错:', error);
                reject(error);
            });
    });
}

// 使用示例（注释掉，因为这里应该是作为模块导出而不是直接调用）
// uploadImg(file, progressBarElement)
//     .then(fileKey => {
//         console.log('上传成功，文件Key:', fileKey);
//     })
//     .catch(error => {
//         console.error('上传失败:', error);
//     });

// 导出函数
export { uploadImg };