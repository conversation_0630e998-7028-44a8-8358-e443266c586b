// face api 计算的一些工具函数
 const calculateFaceQuality = (landmarks) => {
    // 获取关键点
    const positions = landmarks._positions
    
    // 计算眼睛区域的特征
    const leftEye = positions.slice(36, 42)  // 左眼关键点
    const rightEye = positions.slice(42, 48) // 右眼关键点
    
    // 计算眼睛区域的方差（用于判断清晰度）
    const calculateVariance = (points) => {
      const center = points.reduce((acc, p) => ({ _x: acc._x + p._x, _y: acc._y + p._y }), { _x: 0, _y: 0 })
      center._x /= points.length
      center._y /= points.length
      
      const variance = points.reduce((acc, p) => {
        const dx = p._x - center._x
        const dy = p._y - center._y
        return acc + dx * dx + dy * dy
      }, 0) / points.length
      
      return variance
    }
    
    // 计算眼睛区域的方差
    const leftEyeVariance = calculateVariance(leftEye)
    const rightEyeVariance = calculateVariance(rightEye)
    
    // 计算眼睛区域的对称性
    const leftEyeCenter = leftEye.reduce((acc, p) => ({ _x: acc._x + p._x, _y: acc._y + p._y }), { _x: 0, _y: 0 })
    const rightEyeCenter = rightEye.reduce((acc, p) => ({ _x: acc._x + p._x, _y: acc._y + p._y }), { _x: 0, _y: 0 })
    leftEyeCenter._x /= leftEye.length
    leftEyeCenter._y /= leftEye.length
    rightEyeCenter._x /= rightEye.length
    rightEyeCenter._y /= rightEye.length
    
    // 计算眼睛区域的清晰度分数
    const eyeQuality = (leftEyeVariance + rightEyeVariance) / 2
    
    // 根据方差判断清晰度
    let quality = '未知'
    if (eyeQuality > 100) {
      quality = '非常清晰'
    } else if (eyeQuality > 50) {
      quality = '清晰'
    } else if (eyeQuality > 20) {
      quality = '一般'
    } else {
      quality = '模糊'
    }
    
    return {
      quality,
      score: eyeQuality,
      leftEyeVariance,
      rightEyeVariance
    }
  }
  export {
    calculateFaceQuality
  }