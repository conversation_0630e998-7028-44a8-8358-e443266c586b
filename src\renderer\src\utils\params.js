import axios from 'axios';

let baseUrl = import.meta.env.VITE_BASE_URL

let urlParams = {
    u: null, // u参数
    c: null, // c参数
    t: null  // t参数
  }
// 设置axios响应拦截器
axios.interceptors.response.use(
    response => {
      // 如果返回的code不为0，则将其转为错误处理
      if (response.data && response.data.code !== 0) {
        return Promise.reject(response.data);
      }
      return response;
    },
    error => {
      return Promise.reject(error);
    }
  );
// 解析URL参数
async function parseUrlParams() {
    urlParams.u = localStorage.getItem('user');
    urlParams.c = localStorage.getItem('token');
    const searchParams = new URLSearchParams(window.location.search);
    urlParams.u = searchParams.get('u') || urlParams.u;
    urlParams.c = searchParams.get('c') || urlParams.c;
    urlParams.t = searchParams.get('t');
    if (!urlParams.u) {
        console.log('未获取到u参数');
        return {}
    }
    console.log('URL参数:', urlParams);
    axios.defaults.headers['Authorization'] = `Bearer ${urlParams.c}`;
    axios.defaults.baseURL = baseUrl;
    localStorage.setItem('token', urlParams.c);
    localStorage.setItem('user', urlParams.u);
    let result = await axios.get('/api/v1.0/user/get_digital_avatar_info',{
        params:{
            digital_avatar_id: urlParams.u
        }
    }).then(res => {
        try{
            let aid = res.data.data['digital_avatar_id']
            let lid = res.data.data['lucky_id']
            if (aid && lid){
                axios.post('/api/v1.0/user/bind',{
                    digital_avatar_id: aid,
                    lucky_id: lid
                }).then(res => {
                    console.log(res)
                    console.log('bind success')
                }).catch(err => {
                    console.log(err)
                })
            }
        }catch(err){
            console.log(err)
        }
        
        return res
    })
    let data = result.data.data
    
    return {
        avatar: data,
        token: urlParams.c,
        id: urlParams.u
    }
}
async function getAvatarInfo() {
    let result = await axios.get('/api/v1.0/user/get_user_info',{
    }).then(res => {
        return res
    }).catch(err => {
        console.log('need login')
        localStorage.setItem('token', '')
        // wx.miniProgram.redirectTo({ url: '/pages/login2/index' });
    })
    return result.data.data
}
export { parseUrlParams, urlParams, getAvatarInfo };