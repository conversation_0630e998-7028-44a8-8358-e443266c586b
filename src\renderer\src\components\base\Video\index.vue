<template>
  <video :style="extraStyle" ref="videoRef" width="100%" height="100%" autoplay playsinline></video>
</template>

<script setup>
import { useCameraStore } from '@/store/camera'
import {ref,onMounted} from 'vue'
const cameraStore = useCameraStore();
const myEmits = defineEmits(['initFinished'])
const myProps = defineProps({
    extraStyle:{
        type:Object,
        default:()=>{
            return {

            }
        }
    },
    cameraType:{
        type:String,
        default:'common'
    }
})
const videoRef = ref(null)
const cameraTypeObj = {
  common:{
    variable:'cameraCommonStream',
    loadFn:'loadCommonStream',
    setFn:'setCommonStream'
  },
  snap:{
    variable:'cameraSnapStream',
    loadFn:'loadSnapStream',
    setFn:'setSnapStream'
  }
}
const initCamera = async()=>{
  if(cameraStore[cameraTypeObj[myProps.cameraType].variable]){
    videoRef.value.srcObject = cameraStore[cameraTypeObj[myProps.cameraType].variable];
    myEmits('initFinished',videoRef.value)
  }else {
    const stream = await cameraStore[cameraTypeObj[myProps.cameraType].loadFn]();
        if (stream) {
          // 存放在store中
          cameraStore[cameraTypeObj[myProps.cameraType].setFn](stream)
          videoRef.value.srcObject = stream;
          myEmits('initFinished',videoRef.value)
        }
  }
}
onMounted(()=>{
  console.log('渲染2')
    initCamera();
})
</script>

<style lang="less" scoped>
 video {
     object-fit: cover; /* 填充容器 */
     background: #000; /* 黑底占位 */
 }
</style>