import fs from 'fs'
import path from 'path'
import { execSync } from 'child_process'

/**
 * 格式化日期为指定格式
 * @param date - 日期对象
 * @param format - 格式字符串，如 'YYMMDDhhmmss'
 * @returns 格式化后的日期字符串
 */
function formatDate(date: Date, format: string): string {
  const year = date.getFullYear().toString().slice(-2)
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  const hours = date.getHours().toString().padStart(2, '0')
  const minutes = date.getMinutes().toString().padStart(2, '0')
  const seconds = date.getSeconds().toString().padStart(2, '0')

  return format
    .replace('YY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('hh', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
}

/**
 * 获取Git短哈希
 * @returns Git短哈希字符串，如果不是Git仓库则返回空字符串
 */
function getGitShortHash(): string {
  try {
    return execSync('git rev-parse --short HEAD', { encoding: 'utf8' }).trim()
  } catch {
    return '' // 不是 git 仓库或执行失败
  }
}

/**
 * 获取当前版本号
 * @returns 当前版本号字符串，如 "1.0.1"
 */
export function getCurrentVersion(): string {
  try {
    const pkgPath = path.resolve(__dirname, '../../package.json')
    const pkg = JSON.parse(fs.readFileSync(pkgPath, 'utf-8'))
    const version = pkg.version
    
    // 如果版本号包含时间戳和哈希，只返回前三位
    const parts = version.split('.')
    if (parts.length >= 3) {
      return parts.slice(0, 3).join('.')
    }
    
    return version
  } catch (error) {
    console.error('获取版本号失败:', error)
    return '1.0.0'
  }
}

/**
 * 获取完整版本信息数组
 * @returns 版本信息数组 [主版本号, 次版本号, 补丁版本号, 构建时间戳, Git哈希]
 */
export function getVersions(): string[] {
  try {
    const pkgPath = path.resolve(__dirname, '../../package.json')
    const pkg = JSON.parse(fs.readFileSync(pkgPath, 'utf-8'))
    const version = pkg.version
    
    const parts = version.split('.')
    
    if (parts.length >= 5) {
      // 完整版本格式: 1.0.1.timestamp.hash
      return [parts[0], parts[1], parts[2], parts[3], parts[4]]
    } else if (parts.length >= 3) {
      // 基础版本格式: 1.0.1
      return [parts[0], parts[1], parts[2], formatDate(new Date(), 'YYMMDDhhmmss'), getGitShortHash()]
    } else {
      // 异常情况
      return ['1', '0', '0', formatDate(new Date(), 'YYMMDDhhmmss'), getGitShortHash()]
    }
  } catch (error) {
    console.error('获取版本信息失败:', error)
    return ['1', '0', '0', formatDate(new Date(), 'YYMMDDhhmmss'), '']
  }
}

/**
 * 递增版本号的第三位数字
 * @param version - 当前版本号，如 "1.0.0" 或 "1.0.0.timestamp.hash"
 * @returns 递增后的版本号，如 "1.0.1.timestamp.hash"
 */
export function incrementPatchVersion(version: string): string {
  // 提取基础版本号（前三位）
  const parts = version.split('.')
  const baseParts = parts.slice(0, 3) // 只取前三位：major.minor.patch

  if (baseParts.length >= 3) {
    baseParts[2] = (parseInt(baseParts[2]) + 1).toString()
  }

  // 构建新版本号：基础版本 + 时间戳 + Git哈希
  let newVersion = baseParts.join('.')
  newVersion += `.${formatDate(new Date(), 'YYMMDDhhmmss')}.${getGitShortHash()}`
  return newVersion
}

/**
 * 更新package.json中的版本号
 * @param newVersion - 新的版本号
 */
export function updatePackageVersion(newVersion: string): void {
  const pkgPath = path.resolve(__dirname, '../../package.json')
  const pkg = JSON.parse(fs.readFileSync(pkgPath, 'utf-8'))
  const oldVersion = pkg.version
  pkg.version = newVersion
  fs.writeFileSync(pkgPath, JSON.stringify(pkg, null, 2) + '\n')
  console.log(`✅ Version updated: ${oldVersion} → ${newVersion}`)
}

/**
 * 自动递增版本号并更新package.json
 * @returns 新的版本号
 */
export function incrementAndUpdateVersion(): string {
  const pkgPath = path.resolve(__dirname, '../../package.json')
  const pkg = JSON.parse(fs.readFileSync(pkgPath, 'utf-8'))
  const currentVersion = pkg.version
  const newVersion = incrementPatchVersion(currentVersion)
  updatePackageVersion(newVersion)
  return newVersion
}
