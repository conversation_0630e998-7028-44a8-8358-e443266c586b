import cv2
import numpy as np
import os

# 配置参数
foreground_prefix = "template"
background_prefix = "bg"
output_dir = "output"
image_count = 9

def ensure_output_directory(path: str):
    os.makedirs(path, exist_ok=True)

def load_image_with_alpha(path: str):
    """加载图像（保留 alpha 通道）"""
    image = cv2.imread(path, cv2.IMREAD_UNCHANGED)
    if image is None:
        raise FileNotFoundError(f"无法读取图像: {path}")
    return image

def resize_to_match(fg, bg):
    """将前景图 resize 成与背景图一致"""
    return cv2.resize(fg, (bg.shape[1], bg.shape[0]), interpolation=cv2.INTER_LANCZOS4)

def alpha_blend(bg, fg):
    """使用 Alpha 通道将前景叠加到背景上"""
    if fg.shape[2] != 4:
        raise ValueError("前景图必须包含 alpha 通道 (BGRA)")

    # 拆分前景图的 BGR 和 Alpha 通道
    fg_rgb = fg[..., :3].astype(float)
    alpha = fg[..., 3].astype(float) / 255.0
    alpha = np.stack([alpha] * 3, axis=-1)  # 扩展为 3 通道以匹配 RGB

    # 确保背景为 float 类型
    bg = bg.astype(float)

    # Alpha 混合
    blended = alpha * fg_rgb + (1 - alpha) * bg
    return blended.astype(np.uint8)

def process_images():
    ensure_output_directory(output_dir)

    for i in range(1, image_count + 1):
        fg_path = f"{foreground_prefix}{i}.png"
        bg_path = f"{background_prefix}{i}.png"
        output_path = os.path.join(output_dir, f"template{i}.png")

        try:
            fg = load_image_with_alpha(fg_path)
            bg = load_image_with_alpha(bg_path)

            # 如果背景没有 alpha 通道，只取前3个通道
            if bg.shape[2] == 4:
                bg = bg[..., :3]

            fg_resized = resize_to_match(fg, bg)
            result = alpha_blend(bg, fg_resized)
            cv2.imwrite(output_path, result)
            print(f"✅ 合成成功：{output_path}")
        except Exception as e:
            print(f"❌ 合成失败 ({fg_path} + {bg_path})：{e}")

if __name__ == "__main__":
    process_images()
