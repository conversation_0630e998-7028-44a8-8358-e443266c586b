appId: com.electron.app.prod
productName: popofifi-prod
directories:
  buildResources: build
files:
  - '!**/.vscode/*'
  - '!src/*'
  - '!electron.vite.config.{js,ts,mjs,cjs}'
  - '!{.eslintignore,.eslintrc.cjs,.prettierignore,.prettierrc.yaml,dev-app-update.yml,CHANGELOG.md,README.md}'
  - '!{.env,.env.*,.npmrc,pnpm-lock.yaml}'
  - '!{tsconfig.json,tsconfig.node.json,tsconfig.web.json}'
asarUnpack:
  - resources/**
win:
  target:
    - target: nsis
      arch:
        - x64
  executableName: popofifi-prod
  icon: resources/app.ico
nsis:
  oneClick: false
  perMachine: true
  artifactName: ${name}-prod-${env.npm_package_version}-setup.${ext}
  shortcutName: ${productName}
  uninstallDisplayName: ${productName}
  createDesktopShortcut: always
  installerIcon: resources/app.ico
  uninstallerIcon: resources/app.ico
  include: build/installer-prod.nsh
mac:
  entitlementsInherit: build/entitlements.mac.plist
  extendInfo:
    - NSCameraUsageDescription: Application requests access to the device's camera.
    - NSMicrophoneUsageDescription: Application requests access to the device's microphone.
    - NSDocumentsFolderUsageDescription: Application requests access to the user's Documents folder.
    - NSDownloadsFolderUsageDescription: Application requests access to the user's Downloads folder.
  notarize: false
dmg:
  artifactName: ${name}-prod-${env.npm_package_version}.${ext}
linux:
  target:
    - AppImage
    - snap
    - deb
  maintainer: electronjs.org
  category: Utility
appImage:
  artifactName: ${name}-prod-${env.npm_package_version}.${ext}
npmRebuild: false
# publish:
#   provider: generic
#   url: https://example.com/auto-updates
electronDownload:
  mirror: https://npmmirror.com/mirrors/electron
extraResources:
  - from: './resources/app.ico'  # 打包Windows图标
    to: '../resources'
