import { request } from '@/http'


// 获取所有商品列表
export function getAllGoodsList(){
    return request({
        url: '/api/v1.0/device/get_goods_list',
        method: 'post',
    })
}
// 创建订单
export function createOrder(data){
    return request({
        url: '/api/v1.0/payment/create_order',
        method: 'post',
        data: data,
    }) 
}
// 获取订单的状态
export function getOrderStatus(data){
    return request({
        url: '/api/v1.0/payment/order_status',
        method: 'get',
        params: data,
    }) 
}