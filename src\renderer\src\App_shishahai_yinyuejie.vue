<script>
import 'animate.css'
import bk from '@/assets/yinyuejie/result/bk.png'
export default {
  data(){
    return {
      bk
    }
  },
  beforeCreate(){
    
    let client = localStorage.getItem('clientId')
    window.clientId = client && JSON.parse(client)
    let token = localStorage.getItem('token')
    window.token = token
  },
  methods: {
    toHome() {
      console.log(window.clientId)
      this.$router.push({ path: '/welcome' })
    },
    closeApp() {
      window.electron.ipcRenderer.send('close-app')
    }
  }
}
</script>

<template>
  <div @click="toHome" class="toHome"></div>
  <div @click="closeApp" class="toHome close"></div>
  <img class="bk" :src="bk" >
  <!-- <div>
    <a href="https://vite.dev" target="_blank">
      <img src="/vite.svg" class="logo" alt="Vite logo" />
    </a>
    <a href="https://vuejs.org/" target="_blank">
      <img src="./assets/vue.svg" class="logo vue" alt="Vue logo" />
    </a>
  </div> -->
  <!-- <HelloWorld msg="Vite + Vue" /> -->
  <!-- <router-view /> -->

  <router-view #default="{ route, Component }">
    <transition :enter-active-class="`animate__animated ${route?.meta?.transition}`">
      <component :is="Component" />
    </transition>
  </router-view>
</template>

<style scoped>
.bk {
  height: 100vh;
  width: 100vw;
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
  /* background: url('@/assets/family/images/family-bg.svg') no-repeat center center; */
  background-size: cover;
}
.toHome {
  height: 5vw;
  width: 5vw;
  position: absolute;
  top: 0;
  left: 0;
  background-image: url('./assets/back.png');
  z-index: 9999;
}
.close {
  right: 0;
  left: auto;
}
.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.vue:hover {
  filter: drop-shadow(0 0 2em #42b883aa);
}
</style>
