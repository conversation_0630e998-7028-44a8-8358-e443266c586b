<template>
  <div
    class="camera-box"
    :style="{
      width: myProps.boxStyle.width + 'px',
      height: myProps.boxStyle.height + 'px',
      top: myProps.boxStyle.top + 'px',
      left: myProps.boxStyle.left + 'px'
    }"
  >
    <div
      v-for="(gunItem, gunIndex) in gunStyleArr"
      :key="gunIndex"
      :class="['gun',{'gun-selected':myProps.boxStyle.isSelected}]"
      :style="{ ...gunItem }"
    ></div>
    <div class="center-inner">
      <div :class="['circle',{'circle-selected':myProps.boxStyle.isSelected}]" :style="{ ...circleStyle }">
          <div 
          v-for="(hengItem, hengIndex) in circleHengStyle" 
          :key="hengIndex" 
          :style="{
           ...hengItem
         }"
        class="heng">
        </div>
      
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { funArr, hengArr } from './data.js'
const myEmits = defineEmits(['selected'])
const myProps = defineProps({
  boxStyle: {
    type: Object,
    default: () => {
      return {
        width: 0,
        height: 0,
        top: 0,
        left: 0,
        isSelected:true,
        imageHeight:0,
        imageWidth:0,
        age:'',
        ageType:'',
        gender:''
      }
    }
  }
})

const gunStyleArr = ref([])
const circleStyle = ref({})
const circleHengStyle = ref([])
//  计算样式
const handleComputedStyle = () => {
  gunStyleArr.value = funArr.map((item) => {
    const comWidth = Math.trunc(myProps.boxStyle.width * 0.20)
    const comHeight = Math.trunc(myProps.boxStyle.width * 0.03)
    const flotNum = Math.floor(((comWidth - comHeight) / 2) * 10) / 10
    console.log(flotNum, 'flo')
    return {
      width: comWidth + 'px',
      height: comHeight + 'px',
      [item.position[0]]: item.isNeedCompute ? flotNum + 'px' : 0,
      [item.position[1]]: item.isNeedCompute ? '-' + flotNum + 'px' : 0,
      transform: item.isNeedTransform ? `rotate(90deg)` : ''
    }
  })
  circleStyle.value = {
    width: Math.trunc(myProps.boxStyle.width * 0.2) + 'px',
    height: Math.trunc(myProps.boxStyle.width * 0.2) + 'px',
    borderRadius: '50%',
     borderWidth: Math.trunc(myProps.boxStyle.width * 0.015) + 'px',
     borderStyle: 'solid',
  }
  circleHengStyle.value = hengArr.map((item) => {
    const comWidth = Math.trunc(myProps.boxStyle.width * 0.02)
    const comHeight = Math.trunc(myProps.boxStyle.width * 0.08)
    const flotNum = Math.trunc(myProps.boxStyle.width * 0.02)
    console.log(flotNum, 'flo')
    return {
      width: comWidth + 'px',
      height: comHeight + 'px',
      [item.position[0]]: '-'+flotNum + 'px',
      [item.position[1]]: '50%',
      transform: item.transform
    }
  })
}
const handleClickSelected = ()=>{
  myEmits('selected')
}
onMounted(() => {
  handleComputedStyle();
})
</script>

<style lang="less" scoped>
@gunClolor: rgba(67, 207, 124, 1); // 绿色
@gunClolor2: #FFF; // 绿色
// 新增呼吸动画
@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}
.camera-box {
  position: absolute;
  border-radius: 3%;
  overflow: hidden;
  .gun {
    position: absolute;
    background-color: @gunClolor2;
  }
  .gun-selected {
    background-color: @gunClolor;
  }
  .gun1 {
    // 旋转90度
    transform: rotate(90deg);
    //    top: 0;
    //   left: 0;
    top: calc((22% - 0.1px) / 2);
    left: calc(-1 * (22% - 0.1px) / 2);
  }
  .gun2 {
    top: 0;
    left: 0;
  }
  .gun3 {
    top: 0;
    right: 0;
  }
  .gun4 {
    transform: rotate(90deg);
    // 旋转90度
    top: calc((22% - 0.1px) / 2);
    right: calc(-1 * (22% - 0.1px) / 2);
  }
  .gun5 {
    transform: rotate(90deg);
    bottom: calc((22% - 0.1px) / 2);
    left: calc(-1 * (22% - 0.1px) / 2);
  }
  .gun6 {
    bottom: 0;
    left: 0;
  }
  .gun7 {
    bottom: 0;
    right: 0;
  }
  .gun8 {
    transform: rotate(90deg);
    bottom: calc((22% - 0.1px) / 2);
    right: calc(-1 * (22% - 0.1px) / 2);
  }
  .center-inner {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    .circle {
      position: absolute;
      border-color: @gunClolor2;
       animation: pulse 1.5s infinite;
      .heng {
        position: absolute;
        background-color: #fff;
        // 添加动态效果
      }
    }
    .circle-selected {
      border-color: @gunClolor;
      animation: none;
      .heng {
        display: none;
      }
    }
  }
}
</style>
