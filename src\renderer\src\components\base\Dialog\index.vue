<template>
  <el-drawer
    v-model="showDialog"
    direction="btt"
    v-bind="$attrs"
    :style="{ ...dialogStyle, ...extraStyle }"
    @open="handleOpen"
    @close="handleClose"
    :showClose="false"
  >
    <template #header="{ close }">
      <div class="my-header">
        <div class="close-wraper">
          <img src="@/assets/images/close.svg" @click="close" />
        </div>
      </div>
    </template>
    <template #default>
      <slot></slot>
    </template>
  </el-drawer>
</template>

<script setup>
import { ElDrawer } from 'element-plus'
import { ref } from 'vue'
const showDialog = defineModel({ default: false })
defineOptions({
  inheritAttrs: false
})

const dialogStyle = ref({
  width: '94%',
  left: '3%',
  bottom: '3%',
  borderRadius: '36rem',
  backdropFilter: 'blur(40rem)',
  backgroundColor: 'rgba(255,255,255,0.7)',
  padding: '15rem',
  boxSizing: 'border-box',
  transition: 'all 0.3s'
})
const extraStyle = ref({})
const handleClose = () => {
  extraStyle.value = {
    transform: 'translateY(106%) !important'
  }
}
const handleOpen = () => {
  extraStyle.value = {
    transform: 'translateY(0%) !important'
  }
}
</script>

<style lang="less" scoped>
.el-drawer.btt {
  bottom: 13%;
}
.my-header {
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 10rem;
  .close-wraper {
    width: 67rem;
    height: 67rem;
    img {
      width: 100%;
      height: 100%;
    }
  }
}
.el-drawer-fade-leave-to .btt {
  transform: translateY(106%);
}
</style>
