<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="Eslint" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="HtmlUnknownTag" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="myValues">
        <value>
          <list size="7">
            <item index="0" class="java.lang.String" itemvalue="nobr" />
            <item index="1" class="java.lang.String" itemvalue="noembed" />
            <item index="2" class="java.lang.String" itemvalue="comment" />
            <item index="3" class="java.lang.String" itemvalue="noscript" />
            <item index="4" class="java.lang.String" itemvalue="embed" />
            <item index="5" class="java.lang.String" itemvalue="script" />
            <item index="6" class="java.lang.String" itemvalue="style" />
          </list>
        </value>
      </option>
      <option name="myCustomValuesEnabled" value="true" />
    </inspection_tool>
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="22">
            <item index="0" class="java.lang.String" itemvalue="dataclasses" />
            <item index="1" class="java.lang.String" itemvalue="tqdm" />
            <item index="2" class="java.lang.String" itemvalue="scikit_image" />
            <item index="3" class="java.lang.String" itemvalue="torch" />
            <item index="4" class="java.lang.String" itemvalue="numpy" />
            <item index="5" class="java.lang.String" itemvalue="einops" />
            <item index="6" class="java.lang.String" itemvalue="skimage" />
            <item index="7" class="java.lang.String" itemvalue="opencv_python" />
            <item index="8" class="java.lang.String" itemvalue="Pillow" />
            <item index="9" class="java.lang.String" itemvalue="scipy" />
            <item index="10" class="java.lang.String" itemvalue="ipdb" />
            <item index="11" class="java.lang.String" itemvalue="joblib" />
            <item index="12" class="java.lang.String" itemvalue="chumpy" />
            <item index="13" class="java.lang.String" itemvalue="matplotlib" />
            <item index="14" class="java.lang.String" itemvalue="open3d" />
            <item index="15" class="java.lang.String" itemvalue="loguru" />
            <item index="16" class="java.lang.String" itemvalue="torch3d" />
            <item index="17" class="java.lang.String" itemvalue="moviepy" />
            <item index="18" class="java.lang.String" itemvalue="scikit-image" />
            <item index="19" class="java.lang.String" itemvalue="opencv-python" />
            <item index="20" class="java.lang.String" itemvalue="pyaml" />
            <item index="21" class="java.lang.String" itemvalue="future" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyPep8NamingInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredErrors">
        <list>
          <option value="N803" />
        </list>
      </option>
    </inspection_tool>
  </profile>
</component>