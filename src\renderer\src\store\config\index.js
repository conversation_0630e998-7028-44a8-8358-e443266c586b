import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { ElMessage } from 'element-plus'

import {init} from './initConfig'

const config = ref({})
const name = ref({})
const pwd = ref({})

const setPwd = (data) => {
  pwd.value = data
}
const setConfig = (data) => {
  config.value = data
}
const setName = (data) => {
  name.value = data
}

const loadConfig = () => {
  // 判断当前的环境是浏览器环境还是应用环境
  // 根据不同的环境选择不同的做法
  // 如果是应用环境则读配置
  return new Promise(async (resolve, reject) => {
    try {
      if (window.api && typeof window.api !== 'undefined') {
        //    获取应用的配置
        const config = await window.api.getConfig()
        //   读取机器码
        //   优先从配置文件中取，如果配置文件中没有的话，就用机器码
        //   拿到配置
        const appConfig = config.APP
        setConfig(appConfig)
        setName(appConfig.name)
        if (appConfig.pwd) {
          // 如果密码存在的话
          setPwd(appConfig.pwd)
        } else {
          // 如果密码不存在的话，就取用生成的机器码
          const machineInfo = await window.api.getMachineInfo()
          setPwd(machineInfo.machineId)
        }
      } else {
        //   如果是浏览器的环境则直接import取数据
        const config = await import('../../../../../config.json')
        const appConfig = config.APP
        setConfig(appConfig)
        setName(appConfig.name)
        setPwd(appConfig.pwd)
      }
      resolve(true)
    } catch (error) {
      ElMessage.error('读取配置文件失败')
      reject(error)
    }
  })
}

// 配置相关
export const useConfigStore = defineStore('config', () => {
  return { config, name, pwd, loadConfig, setConfig, init }
})
