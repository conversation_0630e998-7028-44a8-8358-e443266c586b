<script>
import axios from 'axios'
import { useConfigStore } from '@/store/config'
import initConfig from './getDefaultConfig.js'
import { ElMessage } from 'element-plus'
import UpdateChecker from '@/components/UpdateChecker.vue'
export default {
  components: {
    UpdateChecker
  },
  data() {
    return {
      id: 'NL_001',
      machineId: '93725328-f17b-4874-a596-439d4c56eb9f',
      env: import.meta.env.VITE_MODE,
      showUpdateChecker: false
    }
  },
  mounted() {
    this.handleInit()
    // 延迟显示更新检查器，避免影响登录流程
    setTimeout(() => {
      this.showUpdateChecker = true
    }, 1000)
  },
  methods: {
    async init() {
      let configStore = useConfigStore()
      if (this.env == 'dev') {
        this.id = 'TZG_002'
        this.machineId = '98283ace992385'
        return
      }
      // if (window.env == 'development'){
      //   this.id = 'MEET_01'
      //   this.machineId = '********************************'
      //   return
      // }
      console.log(configStore?.config)
      this.id = configStore?.config?.APP?.name || 'NG_001'
      let machineInfo = await window.api?.getMachineInfo()
      console.log(machineInfo)
      this.machineId = configStore?.config?.APP?.pwd || machineInfo?.machineId || '98283ace9999999'
    },
    //  读取配置文件写入store
    async handleInit() {
      let configStore = useConfigStore()
      const res = await configStore.loadConfig()
      if (res) {
        this.id = configStore.name
        this.machineId = configStore.pwd
      }
    },

    async register() {
      let configStore = useConfigStore()
      // 固定配置
      const appid = 'qapi_d3f79d77442572b4'
      const appsecret = '43897e48112d4d1ab106e3f981ae4fae'
      const url = '/api/v1.0/device/login'

      // 请求体
      const data = {
        device_id: this.id,
        device_serial_no: this.machineId,
        device_type: 'screen'
      }

      // 工具函数
      function generateNonce(length = 8) {
        const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
        let nonce = ''
        for (let i = 0; i < length; i++) {
          nonce += chars.charAt(Math.floor(Math.random() * chars.length))
        }
        return nonce
      }

      // 时间戳 & 随机数
      const timestamp = String(Date.now())
      const nonce = generateNonce()

      // 签名内容
      const body = JSON.stringify(data)
      const message = body + appid + timestamp + nonce

      // 计算 HMAC-SHA256 签名
      // 需要使用 Web Crypto API
      try {
        const key = await crypto.subtle.importKey(
          'raw',
          new TextEncoder().encode(appsecret),
          { name: 'HMAC', hash: { name: 'SHA-256' } },
          false,
          ['sign']
        )

        const signatureBuffer = await crypto.subtle.sign(
          'HMAC',
          key,
          new TextEncoder().encode(message)
        )

        // 将 ArrayBuffer 转换为十六进制字符串
        const signature = Array.from(new Uint8Array(signatureBuffer))
          .map((b) => b.toString(16).padStart(2, '0'))
          .join('')

        // 构建请求参数
        const params = new URLSearchParams({
          app_id: appid,
          timestamp: timestamp,
          nonce: nonce,
          sign: signature
        })

        // 发送请求
        axios
          .post(`${url}?${params.toString()}`, body)
          .then(async (response) => {
            console.log('Registration successful:', response.data)
            let token = response.data.data.token
            let datas = response.data.data
            localStorage.setItem('clientId', JSON.stringify(datas))
            window.clientId = datas
            window.token = token
            localStorage.setItem('token', token)
            window.api?.setlocalConfig('name', this.id)
            try {
              let conf = await initConfig()
              console.log(conf)
              await configStore.init()
              this.$router.push({
                path: '/welcome'
              })
            } catch (e) {
              ElMessage.error('获取配置失败，请重试')
              return
            }
          })
          .catch((error) => {
            ElMessage.error(error.msg)
            console.error('Error during registration:', error)
          })
      } catch (error) {
        
        console.error('Error during registration:', error)
      }
    }
  }
}
// localStorage.setItem('clientId', 'client_12345');
</script>

<template>
  <div class="login-container">
    <div class="login-form">
      <div class="env-info">{{ env }}</div>
      <div class="device-register">
        <label>注册设备：</label>
        <input v-model="id" class="device-input" />
        <button @click="register()" class="register-btn">注册</button>
      </div>
    </div>

    <!-- 更新检查器 -->
    <div v-if="showUpdateChecker" class="update-section">
      <UpdateChecker
        :auto-check="true"
        :show-details="true"
        update-server-url="http://localhost:8080"
      />
    </div>
  </div>
</template>

<style scoped>
.login-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 20px;
  gap: 30px;
}

.login-form {
  text-align: center;
}

.env-info {
  font-size: 30rem;
  margin-bottom: 20px;
  color: #666;
}

.device-register {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.device-register label {
  font-size: 30rem;
  color: #333;
}

.device-input {
  font-size: 24rem;
  padding: 10px 15px;
  border: 2px solid #ddd;
  border-radius: 8px;
  width: 300px;
  text-align: center;
}

.register-btn {
  font-size: 28rem;
  padding: 12px 30px;
  background: #409eff;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: background 0.3s;
}

.register-btn:hover {
  background: #337ecc;
}

.update-section {
  width: 100%;
  max-width: 600px;
  margin-top: 20px;
}

/* 保持原有的大字体样式用于兼容 */
* {
  font-size: 30rem;
}
</style>
