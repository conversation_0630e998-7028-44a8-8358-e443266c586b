<template>
  <div class="themeSelect">
    <div class="logo-content">
      <div class="title-content">
        <div class="my-title">请点击选择卡通形象</div>
      </div>
      <img class="my-img" src="@/assets/images/new-logo.png" alt="" />
    </div>

    <div class="back-content">
      <div class="backBtn" @click="back">❮ 返回上一步</div>
    </div>
    <div class="card-content">
      <div class="left">
        <div class="gender-content">
          <div class="gender-item" @click="gender = 'female'">
            <img
              class="gender-item-img"
              :src="gender == 'female' ? selectFemale : unSelectFemale"
              alt=""
            />
          </div>
          <div class="gender-item" @click="gender = 'male'">
            <img
              class="gender-item-img"
              :src="gender == 'male' ? selectMale : unSelectMale"
              alt=""
            />
          </div>
        </div>
      </div>
      <div class="right">
        <div class="item-conetnt">
          <div
            v-for="(item, index) in imgList.slice(0, 9)"
            :key="index"
            @click="toCapture(item)"
            class="inner-item"
          >
            <div class="bg-img">
              <img class="my-img" :src="item.url" alt="" />
            </div>
            <!-- <div class="infoBox">
              <div class="name">
                {{ item.name }}<span class="ori-price">￥{{ item.price }}</span>
              </div>
              <div class="price">限时特惠￥{{ item.vipPrice }}</div>
            </div> -->
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import selectMale from '@/assets/images/select-male.svg'
import unSelectMale from '@/assets/images/unselect-male.svg'
import selectFemale from '@/assets/images/select-female.svg'
import unSelectFemale from '@/assets/images/unselect-female.svg'
// import { imgListArr } from '@/views/popfifi/gridData.js'
import { useSinglePhotoStore } from '@/store/single'
export default {
  name: 'themeSelect',
  data() {
    return {
      subjectFile: null,
      imgList: [],
      gender: 'female',
      selectMale,
      unSelectMale,
      selectFemale,
      unSelectFemale
    }
  },
  created() {
    const useSingleStore = useSinglePhotoStore()
    console.log(useSingleStore)

    this.gender = useSingleStore.singlePhotoData.gender || 'female'
    this.imgList = useSingleStore.stylesData
    // 设置全局样式控制
    // this.$store.state.imgStyleTwo = true;
    // this.$store.state.imgStyleThree = false;
    // this.subjectFile = history.state?.subjectFile
    // if (!this.subjectFile) {
    //   console.error('未获取到 subjectFile，请返回重新拍照')
    // }
  },
  methods: {
    async toCapture(item) {
      const useSingleStore = useSinglePhotoStore()
      console.log(useSingleStore)
      let sex = this.gender == 'female' ? 0 : 1
      //sex list
      let sameSexs = this.imgList.filter((i) => i.sex == sex)
      let notSameSexs = this.imgList.filter((i) => i.sex != sex)
      let len = sameSexs.length - 5
      if (len === 0) {
      } else if (len > 0) {
        sameSexs.splice(0, 5)
      } else if (len < 0) {
        sameSexs.push(...notSameSexs.slice(0, -len))
      }
      let index = sameSexs.findIndex((i) => i['code_2d'] == item['code_2d'])
      if (index === -1) {
        sameSexs.splice(0, 1)
      } else {
        sameSexs.splice(index, 1)
      }
      sameSexs.unshift(item)

      // console.log(sameSexs)
      this.$router.push({
        path: '/rebuild2-loading',
        query: { code: item['code_2d'] },
        state: {
          subjectFile: useSingleStore.singlePhotoData.photoFile,
          // templateFile: templateFile,
          codes: sameSexs.map((i) => i['code_2d']),
          code: item['code_2d'],
          gender: this.gender
        }
      })
      // try {
      //   // 找到用户选择的图片 URL
      //   const selectedItem = this.imgList.find((item) => item.code === code)
      //   const imgUrl = selectedItem.imgUrl

      //   // ✅ 把模版图变成 File 对象（兼容后端 FormData）
      //   const response = await fetch(imgUrl)
      //   const blob = await response.blob()
      //   const templateFile = new File([blob], `template_${code}.png`, { type: blob.type })
      //   console.info('开始计时：', new Date().getTime())
      //   // ✅ 跳转 loading 页面，使用 history.state 传递两个文件

      // } catch (e) {
      //   console.error('转换模板图为文件失败', e)
      // }
    },

    back() {
      this.$router.push({
        path: '/welcome'
      })
    }
  }
}
</script>

<style scoped lang="less">
.themeSelect {
  width: 100%;
  height: 100%;
  background: url('@/assets/images/new-bk.png') no-repeat center center;
  background-size: cover;
  display: flex;
  flex-direction: column;
  .logo-content {
    width: 100%;
    .my-img {
      width: 100%;
      height: auto;
    }
  }
  .title-content {
    width: 100%;
    position: relative;
    .my-title {
      position: absolute;
      width: 100%;
      left: 0;
      top: 200rem;
      font-size: 58rem;
      font-weight: 400;
      letter-spacing: 3.6rem;
      text-align: center;
      color: #000;
    }
  }
  .back-content {
    padding-top: 80rem;
    padding-left: 30rem;
    font-size: 58rem;
    font-weight: 400;
    color: rgba(250, 246, 240, 1);
    display: flex;
    justify-content: flex-start;
  }
  .card-content {
    flex: 1;
    width: 100%;
    padding: 40rem 80rem 20rem 40rem;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    gap: 30rem;
    .left {
      width: 6%;
      height: 100%;
      display: flex;
      align-items: center;
      .gender-content {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        gap: 180rem;
        .gender-item {
          width: 100%;
          &-img {
            width: 100%;
            height: auto;
          }
        }
      }
    }
    .right {
      height: 100%;
      flex: 1;
      .item-conetnt {
        width: 100%;
        height: 100%;
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 0 7%;
        .inner-item {
          height: 100%;
          display: flex;
          flex-direction: column; // 改为纵向排列
          justify-content: center;
          align-items: center;
          .bg-img {
            height: 100%;
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            .my-img {
              max-width: 100%;
              max-height: 100%;
            }
          }
          .infoBox {
            flex: 1;
            width: 100%;
            color: #fff;
            display: flex;
            flex-direction: column;
            gap: 10rem;
            padding-top: 10rem;
            .name {
              line-height: 34.7rem;
              font-size: 34.7rem;
              font-weight: 700;
              color: rgba(235, 119, 76, 1);
              .ori-price {
                padding-left: 2rem;
              }
            }
            .price {
              font-weight: 400;
              line-height: 20rem;
              font-size: 20rem;
              color: #000;
            }
          }
        }
      }
    }
  }
}
</style>
