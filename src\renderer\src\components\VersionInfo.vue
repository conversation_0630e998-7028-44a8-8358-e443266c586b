<template>
  <div class="version-info">
    <h3>版本信息</h3>
    <div class="version-details">
      <div class="version-item">
        <label>当前版本:</label>
        <span>{{ currentVersion }}</span>
      </div>
      <div class="version-item">
        <label>主版本号:</label>
        <span>{{ versionInfo[0] }}</span>
      </div>
      <div class="version-item">
        <label>次版本号:</label>
        <span>{{ versionInfo[1] }}</span>
      </div>
      <div class="version-item">
        <label>补丁版本号:</label>
        <span>{{ versionInfo[2] }}</span>
      </div>
      <div class="version-item">
        <label>构建时间:</label>
        <span>{{ versionInfo[3] }}</span>
      </div>
      <div class="version-item">
        <label>Git Hash:</label>
        <span>{{ versionInfo[4] || 'N/A' }}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'VersionInfo',
  data() {
    return {
      currentVersion: '',
      versionInfo: []
    }
  },
  async mounted() {
    await this.loadVersionInfo()
  },
  methods: {
    async loadVersionInfo() {
      try {
        if (window.api) {
          // 获取当前版本号
          this.currentVersion = await window.api.getVersion()
          // 获取完整版本信息
          this.versionInfo = await window.api.getVersionInfo()
          console.log('版本信息加载成功:', {
            version: this.currentVersion,
            info: this.versionInfo
          })
        } else {
          console.warn('API不可用，可能在浏览器环境中运行')
          // 在浏览器环境中的fallback
          this.currentVersion = '1.0.0'
          this.versionInfo = ['1', '0', '0', '开发环境', '']
        }
      } catch (error) {
        console.error('加载版本信息失败:', error)
        this.currentVersion = 'Unknown'
        this.versionInfo = ['?', '?', '?', '?', '?']
      }
    }
  }
}
</script>

<style scoped>
.version-info {
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background-color: #f9f9f9;
  max-width: 400px;
}

.version-info h3 {
  margin-top: 0;
  color: #333;
  text-align: center;
}

.version-details {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.version-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #eee;
}

.version-item:last-child {
  border-bottom: none;
}

.version-item label {
  font-weight: bold;
  color: #555;
}

.version-item span {
  color: #333;
  font-family: monospace;
}
</style>
