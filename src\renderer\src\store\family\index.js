import {ref,computed} from 'vue'
import { defineStore } from 'pinia'

const familyPhotoData = ref([]);

const familyOriPic =  ref({});

const setFamilyPhotoData = (data) => {
    familyPhotoData.value = data;
}
const setFamilyOriPic = (data) => {
    console.log('走了么',data)
    familyOriPic.value = data;
}

// 全家福拍照相关
export const useFamilyPhotoStore = defineStore('familyPhoto',()=>{
    return { familyPhotoData,familyOriPic,setFamilyPhotoData,setFamilyOriPic }
})