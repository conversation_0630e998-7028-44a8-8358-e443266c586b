 export function formatPrice(fen) {
    return (fen / 100).toFixed(2)
  }
  
 export function base64ToBlob(base64Data) {
    // 分离MIME类型和base64数据
    const [mimePart, dataPart] = base64Data.split(';base64,');
    const mimeType = mimePart.split(':')[1];
    
    // 解码base64字符串
    const byteString = atob(dataPart);
    
    // 创建ArrayBuffer和视图
    const buffer = new ArrayBuffer(byteString.length);
    const uint8Array = new Uint8Array(buffer);
    
    // 填充二进制数据
    for (let i = 0; i < byteString.length; i++) {
        uint8Array[i] = byteString.charCodeAt(i);
    }
    
    return new Blob([buffer], { type: mimeType });
}
// 获取图片的高度
export function getImageSize (url){
    return new Promise((resolve,reject) => {
      var img = new Image();
      img.src = url;
      img.onload = () => {
        // 为什么要写 onload  是因为要等他加载完之后才能获取到图片宽高
        resolve({ width: img.naturalWidth, height: img.naturalHeight }); //  2064,4608
      };
      img.onerror = (err) => {
       reject(err);
      };
    });
  };