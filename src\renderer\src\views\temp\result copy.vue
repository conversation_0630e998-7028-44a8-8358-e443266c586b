<template>
  <div class="result-page">
    <div class="header">回到纯真年代</div>


    <!-- 左上角按钮 -->
    <div class="button-left-top">
      <button class="back-button" @click="goBack">返回拍照</button>
    </div>

    <!-- 右上角按钮组 -->
    <div class="button-right-top">
      <button class="regen-button" @click="reGenerate" :disabled="isReGenerating">
        <span v-if="isReGenerating">
          <span class="spinner-inline"></span>
          正在生成…
        </span>
        <span v-else>重新生成</span>
      </button>
      <button class="print-button" @click="openPrint">打印</button>
    </div>


    <!-- 主图展示 -->
    <div
      class="main-image-container"
      v-if="results.length"
      :style="{ backgroundImage: `url(${bgImages[(currentIndex % 9)]})` }"
    >
      <img :src="results[currentIndex].result_url" alt="图像展示" />
    </div>


    <!-- 缩略图列表带左右滚动控制 -->
    <div class="thumbnail-wrapper">
      <button class="scroll-btn left" @click="scrollThumbnails(-1)">‹</button>
      <div class="thumbnail-list" ref="thumbnailList">
        <div
          v-for="(item, index) in results"
          :key="item.style"
          class="thumb"
          :class="{ active: currentIndex === index }"
          @click="switchImage(index)"
        >
          <img :src="item.result_url" />
        </div>
      </div>
      <button class="scroll-btn right" @click="scrollThumbnails(1)">›</button>
    </div>



    <!-- 打印组件（浮层展示） -->
    <PrintDialog
      v-if="showPrintDialog"
      :results="results"
      :currentIndex="currentIndex"
      :subjectFile="subjectFile"
      :gender="gender"
      @close="showPrintDialog = false"
      @appendResult="handleAppendResult"
    />
    


  </div>
</template>

<script>
import PrintDialog from "./result_PrintDialog.vue";
import { generateImage } from "@/utils/imageGen";
import axios from "axios";
import bg1 from '@/assets/home/<USER>';
import bg2 from '@/assets/home/<USER>';
import bg3 from '@/assets/home/<USER>';
import bg4 from '@/assets/home/<USER>';
import bg5 from '@/assets/home/<USER>';
import bg6 from '@/assets/home/<USER>';
import bg7 from '@/assets/home/<USER>';
import bg8 from '@/assets/home/<USER>';
import bg9 from '@/assets/home/<USER>';
import makeImg from './result_makeImg.vue'
export default {
  name: "ResultPage",
  components: {
    PrintDialog,makeImg
  },
  data() {
    return {
      results: [],
      currentIndex: 0,
      subjectFile: null,
      gender: "female",
      remainCodes: [],
      isGenerating: false,
      cancelFlag: false,
      templateQueue: [],
      isReGenerating: false,
      showPrintDialog: false,

      lst: [],
      taskId: '',
      avatarId: '',
      interval: null,
      reInterval: null,
      bgImages: [bg1, bg2, bg3, bg4, bg5, bg6, bg7, bg8, bg9],
      state: history.state,
      selectItem: {}
    };
  },
  mounted() {
    const state = history.state;
    if (!state?.results) {
      console.error("没有结果数据");
      this.results = [{"task_id":"1747633303161","sub_task_id":"12AC7F7AA4BD4586F31058D7AE44592C:p_006","digital_avatar_id":"12AC7F7AA4BD4586F31058D7AE44592C","style":"p_006","avatar_image":"tmp/20250519/12/AC/7F7AA4BD4586F31058D7AE44592C/NJGOAGFSE1JO0LYL.jpeg","avatar_http_url":"https://qcard-test.oss-cn-beijing.aliyuncs.com/tmp%2F20250519%2F12%2FAC%******************************%2FNJGOAGFSE1JO0LYL.jpeg?Expires=1747719703&OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&Signature=4vt1NtT4PCc%2FjGIv40RTd6ijzuY%3D","callback":"https://popofifi-test.xcastle.net/api/v1.0/digital-avatar/gen_2d_callback","status":"done","result_oss":"tmp/20250519/12/AC/7F7AA4BD4586F31058D7AE44592C/VGJR9B62H6E2V77U.png","result_url":"https://qcard-test.oss-cn-beijing.aliyuncs.com/tmp%2F20250519%2F12%2FAC%******************************%2FVGJR9B62H6E2V77U.png?Expires=1747719723&OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&Signature=tqYUGurd%2FQf2b82dlk1a71EqbME%3D","code":"006"},{"task_id":"1747633303161","sub_task_id":"12AC7F7AA4BD4586F31058D7AE44592C:p_003","digital_avatar_id":"12AC7F7AA4BD4586F31058D7AE44592C","style":"p_003","avatar_image":"tmp/20250519/12/AC/7F7AA4BD4586F31058D7AE44592C/NJGOAGFSE1JO0LYL.jpeg","avatar_http_url":"https://qcard-test.oss-cn-beijing.aliyuncs.com/tmp%2F20250519%2F12%2FAC%******************************%2FNJGOAGFSE1JO0LYL.jpeg?Expires=1747719703&OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&Signature=4vt1NtT4PCc%2FjGIv40RTd6ijzuY%3D","callback":"https://popofifi-test.xcastle.net/api/v1.0/digital-avatar/gen_2d_callback","status":"done","result_oss":"tmp/20250519/12/AC/7F7AA4BD4586F31058D7AE44592C/4KMHJ0IDBR1O1J4V.png","result_url":"https://qcard-test.oss-cn-beijing.aliyuncs.com/tmp%2F20250519%2F12%2FAC%******************************%2F4KMHJ0IDBR1O1J4V.png?Expires=1747719735&OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&Signature=GC0jS4%2B5DpsI0KbxZ14%2BxCtfkfM%3D","code":"003"},{"task_id":"1747633303161","sub_task_id":"12AC7F7AA4BD4586F31058D7AE44592C:p_004","digital_avatar_id":"12AC7F7AA4BD4586F31058D7AE44592C","style":"p_004","avatar_image":"tmp/20250519/12/AC/7F7AA4BD4586F31058D7AE44592C/NJGOAGFSE1JO0LYL.jpeg","avatar_http_url":"https://qcard-test.oss-cn-beijing.aliyuncs.com/tmp%2F20250519%2F12%2FAC%******************************%2FNJGOAGFSE1JO0LYL.jpeg?Expires=1747719703&OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&Signature=4vt1NtT4PCc%2FjGIv40RTd6ijzuY%3D","callback":"https://popofifi-test.xcastle.net/api/v1.0/digital-avatar/gen_2d_callback","status":"done","result_oss":"tmp/20250519/12/AC/7F7AA4BD4586F31058D7AE44592C/SUBRNS8OZDL0MGHQ.png","result_url":"https://qcard-test.oss-cn-beijing.aliyuncs.com/tmp%2F20250519%2F12%2FAC%******************************%2FSUBRNS8OZDL0MGHQ.png?Expires=1747719741&OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&Signature=7xJS6KpVRG8YsOL9wkwGFieobT8%3D","code":"004"},{"task_id":"1747633303161","sub_task_id":"12AC7F7AA4BD4586F31058D7AE44592C:p_007","digital_avatar_id":"12AC7F7AA4BD4586F31058D7AE44592C","style":"p_007","avatar_image":"tmp/20250519/12/AC/7F7AA4BD4586F31058D7AE44592C/NJGOAGFSE1JO0LYL.jpeg","avatar_http_url":"https://qcard-test.oss-cn-beijing.aliyuncs.com/tmp%2F20250519%2F12%2FAC%******************************%2FNJGOAGFSE1JO0LYL.jpeg?Expires=1747719703&OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&Signature=4vt1NtT4PCc%2FjGIv40RTd6ijzuY%3D","callback":"https://popofifi-test.xcastle.net/api/v1.0/digital-avatar/gen_2d_callback","status":"done","result_oss":"tmp/20250519/12/AC/7F7AA4BD4586F31058D7AE44592C/GU1VWA2TN3XT8HQL.png","result_url":"https://qcard-test.oss-cn-beijing.aliyuncs.com/tmp%2F20250519%2F12%2FAC%******************************%2FGU1VWA2TN3XT8HQL.png?Expires=1747719753&OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&Signature=s1MyVfRpIN3x4OlPiiNZhym5djE%3D","code":"007"},{"task_id":"1747633303161","sub_task_id":"12AC7F7AA4BD4586F31058D7AE44592C:p_009","digital_avatar_id":"12AC7F7AA4BD4586F31058D7AE44592C","style":"p_009","avatar_image":"tmp/20250519/12/AC/7F7AA4BD4586F31058D7AE44592C/NJGOAGFSE1JO0LYL.jpeg","avatar_http_url":"https://qcard-test.oss-cn-beijing.aliyuncs.com/tmp%2F20250519%2F12%2FAC%******************************%2FNJGOAGFSE1JO0LYL.jpeg?Expires=1747719703&OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&Signature=4vt1NtT4PCc%2FjGIv40RTd6ijzuY%3D","callback":"https://popofifi-test.xcastle.net/api/v1.0/digital-avatar/gen_2d_callback","status":"done","result_oss":"tmp/20250519/12/AC/7F7AA4BD4586F31058D7AE44592C/9UO91JSX1NSUJH4X.png","result_url":"https://qcard-test.oss-cn-beijing.aliyuncs.com/tmp%2F20250519%2F12%2FAC%******************************%2F9UO91JSX1NSUJH4X.png?Expires=1747719765&OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&Signature=HCKpgFeNWcsdK0K%2FUY%2B9v9nfzh0%3D","code":"009"}]

      console.log(this.state)
      console.log(this.results)
      return;
    }
    console.log('result')
    this.results = []
    this.lst = state.remainCodes
    this.addImg(state.results)
    this.subjectFile = state.subjectFile;
    this.gender = state.gender || "female";
    this.remainCodes = state.remainCodes || [];
    this.taskId = state.taskId
    this.avatarId = state.avatarId

    const existingCodes = this.results.map(r => r.code);
    this.templateQueue = this.remainCodes.filter(code => !existingCodes.includes(code));
    this.getOthersImage()
    // this.runQueue(); // 启动生成任务队列
  },
  unmounted() {
    this.interval && clearInterval(this.interval);
    this.reInterval && clearInterval(this.reInterval);
  },
  methods: {
    addImg(item){
      let code = item.style.slice(2)
      item.code = code
      let index = this.results.findIndex(e=>e.code == code)
      if (index!= -1){
        this.results.splice(index,1,item)
        return
      }
      if (this.lst.includes(code)){
        this.results.push(item)
      }
    },
    getOthersImage(){
      
      let getImg = e=>{
        if (this.lst.length <= this.results.length){
          clearInterval(this.interval)
          console.info('最后一张：',new Date().getTime())
          return
        }
        axios.post('/api/v1.0/digital-avatar/get_gen_2d_result',{
          "task_id": this.taskId,
          "digital_avatar_id": this.avatarId
        }).then(e=>{
          this.addImg(e.data.data)
          console.info('一张：',new Date().getTime())
        })
      }
      getImg()
      this.interval = setInterval(()=>{
        getImg()
      },1000)

    },
    /////
    getBgImage(index) {
      const bgIndex = (index % 9) + 1;
      let url = new URL(`@/assets/home/<USER>
      console.log(url)
      return url
    },

    openPrint() {
      this.showPrintDialog = true;
    },

    handleAppendResult(newResult) {
      this.results.push(newResult);
    },

    async runQueue() {
      while (this.templateQueue.length > 0) {
        if (this.cancelFlag) {
          console.log("任务中断，等待新指令");
          break;
        }

        const code = this.templateQueue.shift();
        await this.runSingle(code);
      }
    },

    async runSingle(code) {
      this.isGenerating = true;
      const result = await generateImage(code, this.subjectFile);
      this.isGenerating = false;

      if (result) {
        this.results.push(result);
      }
    },

    switchImage(index) {
      this.currentIndex = index;
      this.selectItem = this.results[index];
    },

    scrollThumbnails(direction) {
      // if (direction>0){
      //   if (this.currentIndex == this.results.length-1){
      //     return
      //   }
      //   this.currentIndex++
      //   this.selectItem = this.results[this.currentIndex]
      // }else{
      //   if (this.currentIndex == 0){
      //     return
      //   }
      //   this.currentIndex--
      //   this.selectItem = this.results[this.currentIndex]
      // }
      const container = this.$refs.thumbnailList;
      if (container) {
        const scrollAmount = container.clientWidth / 2;
        container.scrollBy({
          left: direction * scrollAmount,
          behavior: "smooth",
        });
      }
    },

    goBack() {
      this.$router.push({ path: "/rebuild2-capture" });
    },

    async reGenerate() {
      const current = this.results[this.currentIndex];
      const code = current.code;

      this.isReGenerating = true; // ✅ 设置按钮状态

      this.cancelFlag = true;

      let that = this
      let state = history.state;
      let taskId= new Date().getTime().toString()
      axios.post('/api/v1.0/digital-avatar/pre_gen_2d', {
        "client_id": state.client_id,
        "task_id": taskId,
        "digital_avatar_id": state.avatarId,
        "style": ['p_'+current.code],
        "avatar_image": state.objectKey
      }).then(e=>{
        that.interval = setInterval(() => {
          axios.post('/api/v1.0/digital-avatar/get_gen_2d_result',{
            "task_id": taskId,
            "digital_avatar_id": state.avatarId
          }).then(e=>{
            console.log(e)
            clearInterval(that.interval)
            let item = e.data.data
            item.code = code
            that.results[that.currentIndex] = item
            this.isReGenerating = false;
          })
        },1000)
      })
      return
      // 等待当前任务释放资源
      while (this.isGenerating) {
        await new Promise(resolve => setTimeout(resolve, 300));
      }

      // 执行插队任务
      await this.runSingle(code);

      this.cancelFlag = false;
      this.runQueue(); // 恢复队列执行

      this.isReGenerating = false; // ✅ 任务完成后恢复按钮
    },


  },
};
</script>

<style scoped lang="less">
.result-page {
  width: 100vw;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #000;
  position: relative; // 关键：用于内部绝对定位按钮

  .header {
    font-size: 5vw;
    color: #fff;
    margin-top: 3vh;
  }

  .main-image-container {
    margin-top: 14vh;
    width: 60vw;
    height: 100vw;
    border-radius: 12px;
    overflow: hidden;
    background-color: #000;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    display: flex;
    justify-content: center;
    align-items: center;

    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
      display: block;
      z-index: 1;
    }
  }


  .thumbnail-wrapper {
    margin-top: 4vh;
    display: flex;
    align-items: center;
    width: 95vw;
    overflow: hidden;
    margin-top: 2vh;
    position: relative;

    .scroll-btn {
      background: rgba(255, 255, 255, 0.1);
      color: #fff;
      border: none;
      font-size: 6vw;
      padding: 0 2vw;
      cursor: pointer;
      z-index: 2;
      height: 100%;
      transition: background 0.2s;
      border-radius: 6px;

      &.left {
        margin-right: 1vw;
      }

      &.right {
        margin-left: 1vw;
      }

      &:hover {
        background: rgba(255, 255, 255, 0.3);
      }
    }

    .thumbnail-list {
      display: flex;
      flex-wrap: nowrap;
      overflow-x: auto;
      scroll-behavior: smooth;
      gap: 1vw;

      .thumb {
        flex: 0 0 auto;
        border: 2px solid transparent;
        border-radius: 8px;
        cursor: pointer;

        &.active {
          border-color: #ff7875;
        }

        img {
          height: 12vh;
          border-radius: 6px;
          object-fit: cover;
        }
      }
    }
  }

  .button-left-top {
    position: absolute;
    top: 8vh;
    left: 2vw;

    .back-button {
      padding: 10px 20px;
      font-size: 4vw;
      font-weight: 500;
      border-radius: 20px;
      color: white;
      background: linear-gradient(to bottom right, #111, #333);
      border: 2px solid rgba(255, 255, 255, 0.75);
      transition: background 0.3s, border 0.3s, color 0.3s;
      backdrop-filter: blur(4px);
      -webkit-backdrop-filter: blur(4px);

      &:hover {
        background: linear-gradient(to bottom right, #222, #444);
        border-color: rgba(255, 255, 255, 0.6);
      }
    }

  }

  // 右上角按钮组（重新生成 + 打印）
  .button-right-top {
    width: 30vw;
    position: absolute;
    top: 6vh;
    right: 1vw;
    display: flex;
    flex-direction: column;
    gap: 1vh;

    button {
      padding: 10px 0;
      font-size: 4vw;
      font-weight: bold;
      border-radius: 30px;
      text-align: center;
      color: white;
      border: none;
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
      transition: background 0.3s, transform 0.2s;

      &:hover {
        transform: translateY(-2px);
      }
    }

    .regen-button {
      background: linear-gradient(to right, #36d1dc, #5b86e5);

      &:hover {
        background: linear-gradient(to right, #5b86e5, #36d1dc);
      }

      &:disabled {
        background: #999;
        cursor: not-allowed;
        box-shadow: none;
        transform: none;
      }
    }

    .print-button {
      background: linear-gradient(to right, #f7971e, #ffd200);

      &:hover {
        background: linear-gradient(to right, #ffd200, #f7971e);
      }
    }

    .spinner-inline {
      display: inline-block;
      width: 1em;
      height: 1em;
      margin-right: 0.5em;
      border: 2px solid rgba(255, 255, 255, 0.3);
      border-top-color: #fff;
      border-radius: 50%;
      animation: spin 0.8s linear infinite;
      vertical-align: middle;
    }

    @keyframes spin {
      to {
        transform: rotate(360deg);
      }
    }
  }


}

</style>
