<template>
  <div class="home">
    <!-- 背景轮播 Swiper（主控） -->
    <swiper
      ref="swiperBgRef"
      class="background-swiper"
      :loop="true"
      :autoplay="{ delay: 3000 }"
      :speed="600"
      :allow-touch-move="false"
      :slides-per-view="3"
      :modules="[Autoplay]"
    >
    <!-- @slideChange="onBgSlideChange" -->
      <swiper-slide v-for="(img, index) in images" :key="'bg-' + index">
        <img :src="img" class="bg-image" />
      </swiper-slide>
    </swiper>

    <!-- 背景暗化层 -->
    <div class="background-overlay"></div>

    <!-- 顶部标题区域 -->
    <div class="top-text">
      <img src="@/assets/home/<USER>" class="title-img" />
      <img src="@/assets/home/<USER>" class="subtitle-img" />
    </div>

    <!-- 中间轮播图 -->
    <div class="carousel-container">
      <div class="carousel-card">
        <swiper
          ref="swiperRef"
          :loop="true"
          :autoplay="{ delay: 3000 }"
          :allow-touch-move="false"
          :speed="600"
          class="swiper"
          :modules="[Autoplay]"
          @swiper="onSwiperReady"
        >
          <swiper-slide v-for="(img, index) in images" :key="'fg-' + index">
            <img :src="img" class="carousel-image" />
          </swiper-slide>
        </swiper>
      </div>
    </div>

    <!-- 底部按钮区域（略去内部装饰） -->
    <div class="bottom-button" @click="toSelect">
      <img src="@/assets/home/<USER>" class="button-bg" />
      <div class="button-center">
        <img src="@/assets/home/<USER>" class="button-text-img" />
        <img src="@/assets/home/<USER>" class="star star-tl" />
        <img src="@/assets/home/<USER>" class="star star-tr" />
        <img src="@/assets/home/<USER>" class="star star-bl" />
        <img src="@/assets/home/<USER>" class="star star-br" />
        <img src="@/assets/home/<USER>" class="camera-icon" />
      </div>
    </div>
  </div>
</template>

<script>
import { Swiper, SwiperSlide } from 'swiper/vue'
import {Autoplay} from 'swiper/modules'
import 'swiper/css'


export default {
  name: 'HomePage',
  components: { Swiper, SwiperSlide },
  data() {
    return {
      images: [
        new URL('@/assets/home/<USER>', import.meta.url),
        new URL('@/assets/home/<USER>', import.meta.url),
        new URL('@/assets/home/<USER>', import.meta.url),
      ],
      fgSwiper: null, // ✅ 保存前景 swiper 实例
    }
  },
  setup() {
    return {
      Autoplay
    }
  },
  methods: {
    onSwiperReady(swiperInstance) {
      this.fgSwiper = swiperInstance
      console.log('前景 Swiper 准备完成')
    },
    toSelect() {
      this.$router.push({ path: '/rebuild2-capture' })
    }
  },
}
</script>

<style lang="less" scoped>
.home {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;

  .background-swiper {
    position: absolute;
    inset: 0;
    z-index: 0;

    .swiper-slide {
      width: 100%;
      height: 100%;

      .bg-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        filter: blur(3px) brightness(0.65);
      }
    }
  }

  .background-overlay {
    position: absolute;
    inset: 0;
    background-color: rgba(0.6, 0.6, 0.6, 0.2);
    z-index: 1;
  }

  .top-text {
    flex: 2;
    z-index: 2;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .title-img {
      width: 75vw;
      margin-bottom: 0.0vh;
    }

    .subtitle-img {
      width: 70vw;
      margin-bottom: 1.5vh;
      background-color: white; // 添加白色背景
      padding: 0.8vh 1.5vw;       // 可选：添加内边距让文字不贴边
      border-radius: 8px;       // 可选：圆角更美观
    }

  }

  .carousel-container {
    flex: 5;
    z-index: 2;
    display: flex;
    justify-content: center;
    align-items: center;

    .carousel-card {
      background: white;
      padding: 9px;
      border-radius: 20px;
      box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
      width: 65vw;

      .carousel-image {
        width: 100%;
        border-radius: 10px;
        object-fit: cover;
        display: block;
      }
    }
  }

  .bottom-button {
    flex: 2;
    position: relative;
    width: 72vw;
    aspect-ratio: 720 / 180;
    margin-bottom: 3vh;
    z-index: 2;
    display: flex;
    justify-content: center;
    align-items: center;

    .button-bg {
      position: absolute;
      width: 100%;
      height: 100%;
      object-fit: contain;
      top: 0;
      left: 0;
    }

    .button-center {
      position: relative;
      width: fit-content;
      margin: 0 auto;

      .button-text-img {
        width: 46vw;
        display: block;
      }

      .star,
      .camera-icon {
        position: absolute;
        width: 5vw;
        height: auto;
        object-fit: contain;
      }

      .star-tl {
        top: -33%;
        left: 6%;
        transform: scale(0.5);
      }

      .star-tr {
        top: 46%;
        right: -2%;
        transform: scale(0.5);
      }

      .star-bl {
        bottom: 38%;
        left: 2%;
        transform: scale(0.3);
      }

      .star-br {
        bottom: -37%;
        right: 2%;
        transform: scale(0.3);
      }

      .camera-icon {
        bottom: 23%;
        right: -13%;
        width: 6.5vw;
      }
    }
  }
}
</style>
