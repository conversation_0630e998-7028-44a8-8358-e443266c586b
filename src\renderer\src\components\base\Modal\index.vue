<template>
   <el-dialog
    v-model="showDialog"
    v-bind="$attrs"
    align-center
    :style="{ ...dialogStyle, ...myProps.extraStyle }"
    :showClose="false"
  >
     <template v-if="myProps.isShowTitle" #header="{ close }">
      <div class="my-header">
        <div class="close-wraper">
          <img src="@/assets/images/close.svg" @click="close" />
        </div>
      </div>
    </template>
    <template #default>
      <slot></slot>
    </template>
  </el-dialog>
</template>

<script setup>
import { ElDialog } from 'element-plus'
import { ref } from 'vue'
const showDialog = defineModel({ default: false });
defineOptions({
  inheritAttrs: false
})
const myProps = defineProps({
    isShowTitle:{
        type:Boolean,
        default:true
    },
    extraStyle:{
        type:Object,
        default:()=>{
            return {}
        }
    }
})

const dialogStyle = ref({
  borderRadius: '36rem',
  backdropFilter: 'blur(40rem)',
  backgroundColor: 'rgba(255,255,255,0.7)',
  padding: '15rem',
  boxSizing: 'border-box',
})
</script>

<style lang="less" scoped>
.my-header {
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 10rem;
  box-sizing: border-box;
  .close-wraper {
    width: 67rem;
    height: 67rem;
    img {
      width: 100%;
      height: 100%;
    }
  }
}
</style>