<template>
  <div class="purchase">
    <div class="top" id="myTop">
      <!-- <div class="logo">
        <img class="img-logo" src="@/assets/9_9/images/logo.png" alt="" />
      </div> -->
      <img class="my-img" src="@/assets/yinyuejie/welcome/bg_top.png" alt="" />
      <div class="inner-camera">
        <div class="video-wraper">
          <base-video
            :extraStyle="{
              transform: 'scaleX(-1) rotate(90deg) scale(1.35)'
            }"
            cameraType="snap"
            @initFinished="handleInitFinished"
          ></base-video>
          <img v-if="capturedImage" class="preview-image" :src="capturedImage" />
        </div>
      </div>
      <div v-if="countdown != 0" class="count-numer">
        {{ countdown }}
      </div>
      <!-- <div class="guang-inner">
        <div class="guang-box">
          <div class="guang"></div>
        </div>
      </div> -->
    </div>
    <activity-bottom
      goodCode="V1_TICKED_OFF"
      :countdown="5"
       :themeImg="{
        bgImg:bgBottom,
        btnImg:btnBg,
        outerCircleBgImg:btnWraper,
        reTakeBtnTextImg:retakeText,
        backBtnTextImg:backText,
        purchaseTextImg:purchaseText,
        sureBtnTextImg:sureText,
      }"
      @countDecrease="handleCountDecrease"
      @countFinished="handleCountFinished"
      @handleCreateOrder="handleReceivedCreateOrder"
      @handleRetake="handleReceivedRetake"
      @handleBack="handleClickBack"
    >
    </activity-bottom>
  </div>
</template>

<script>
import domToImage from 'dom-to-image'
import { uploadImg } from '@/views/popfifi/uploadImg.js'
import { generateUUID } from '@/utils/uuid'
import { base64ToBlob } from '@/utils'
import { snapdom } from '@zumer/snapdom'
import { ElMessage } from 'element-plus'
import bus from '@/utils/bus'
import ActivityBottom from '@/components/activity/Bottom/index.vue'
import BaseVideo from '@/components/base/Video/index.vue'
import bgBottom from  '@/assets/yinyuejie/welcome/bg_bottom.png'
import btnBg from  '@/assets/yinyuejie/bottom/btn-bg.png'
import btnWraper from  '@/assets/yinyuejie/bottom/btn-wraper.png'
import retakeText from  '@/assets/yinyuejie/bottom/retake-text.png'
import backText from  '@/assets/yinyuejie/bottom/back-text.png'
import purchaseText from  '@/assets/yinyuejie/bottom/purchase-text.png'
import sureText from  '@/assets/yinyuejie/bottom/sure-text.png'
export default {
  components: { ActivityBottom, BaseVideo },
  data() {
    return {
      countdown: 0, // 新增倒计时数据
      capturedImage: null,
      currentGoodItem: {},
      myCanvas: null,
      snapVideoRef: null,
      snapCameraInited: false,
       bgBottom,
      btnBg,
      btnWraper,
      retakeText,
      backText,
      purchaseText,
      sureText,
    }
  },
  watch: {
    snapCameraInited(newVal) {
      if (newVal) {
        bus.emit('cameraInitFinished')
      }
    }
  },
  methods: {
    // 收到basevideo初始化完成的回调
    handleInitFinished(videoRef) {
      console.log('加载完')
      this.snapVideoRef = videoRef
      this.myCanvas = document.createElement('canvas')
      this.snapCameraInited = true
    },
    // 新增拍照方法
    capturePhoto() {
      const video = this.snapVideoRef
      const canvas = this.myCanvas || document.createElement('canvas')
      canvas.width = video.videoWidth
      canvas.height = video.videoHeight
      const ctx = canvas.getContext('2d')
      ctx.save()
      ctx.drawImage(video, 0, 0, video.videoWidth, video.videoHeight)
      ctx.restore() // 恢复画布状态
      // 保存base64图片数据
      const imageData = canvas.toDataURL('image/jpeg')
      this.capturedImage = imageData
      video.pause()
      bus.emit('cameraCaptureFinished') // 发送流到父组件
    },

    handleDomToImageCopy() {
      let that = this
      const targetElement = document.getElementById('myTop')
      // 获取原始尺寸
      const originalWidth = targetElement.offsetWidth
      const originalHeight = targetElement.offsetHeight
      // 设置最大尺寸
      const maxSize = 1024
      let scale = 1
      // 计算缩放比例
      if (originalWidth > maxSize || originalHeight > maxSize) {
        scale = Math.min(maxSize / originalWidth, maxSize / originalHeight)
      }
      this.$nextTick(async () => {
        try {
          const img = await snapdom.toJpg(targetElement, {
            dpr: scale
          })
          if (img) {
            const blob = base64ToBlob(img.src)
            const file = new File([blob], `${generateUUID()}.jpg`, { type: 'image/jpg' })
            let upRes = await uploadImg(file)
            console.log(upRes, 'dsdsdsd')
            upRes.objectKey && that.handleComputedOrderData(upRes)
          }
        } catch (error) {
          ElMessage({
            message: '发生错误:domToImage',
            type: 'error'
          })
          console.error('oops, something went wrong!', error)
        }
      })
      // domToImage
      //   .toJpeg(targetElement, {
      //     width: originalWidth * scale,
      //     height: originalHeight * scale,
      //     style: {
      //       transform: `scale(${scale})`,
      //       transformOrigin: 'top left',
      //       overflow: 'visible'
      //     },
      //     quality: 0.95
      //   })
      //   .then(async function (base64) {
      //     console.log(base64, 'base64')
      //     // 使用封装好的转换函数
      //     const blob = base64ToBlob(base64)
      //     const file = new File([blob], `${generateUUID()}.jpg`, { type: 'image/jpg' })
      //     let upRes = await uploadImg(file)
      //     console.log(upRes, 'dsdsdsd')
      //     upRes.objectKey && that.handleComputedOrderData(upRes)
      //   })
      //   .catch(function (error) {
      //     ElMessage({
      //       message: '发生错误:domToImage',
      //       type: 'error'
      //     })
      //     console.error('oops, something went wrong!', error)
      //   })
    },
    // 组装创建订单的数据给组件
    handleComputedOrderData(upRes) {
      const { id, type, price } = this.currentGoodItem
      const goodData = [
        {
          goods_id: id,
          goods_num: 1,
          goods_type: type,
          style: type,
          pic_url: upRes.objectKey,
          discount_id: null
        }
      ]
      let pramsObj = {
        business_data: goodData,
        payment_amount: price,
        original_amount: price,
        discount_id: null,
        origi_pic_url: upRes.objectKey,
        digital_avatar_id: upRes.avatarId
      }
      bus.emit('orderData', pramsObj)
    },
    handleReceivedCreateOrder(currentGoodItem) {
      this.currentGoodItem = currentGoodItem
      this.handleDomToImageCopy()
    },
    handleReceivedRetake() {
      this.capturedImage = null
      this.snapVideoRef.play()
    },
    handleClickBack() {
      this.$router.push({ path: '/welcome' })
    },
    handleCountDecrease(count) {
      this.countdown = count
    },
    handleCountFinished() {
      this.capturePhoto()
    }
  }
}
</script>

<style lang="less" scoped>
@font-face {
  font-family: 'DouyinSansBold';
  src: url('@/assets/fonts/DouyinSansBold.ttf') format('truetype');
}
@property --angle {
  syntax: '<angle>';
  inherits: false;
  initial-value: 0deg;
}
@keyframes rotate {
  to {
    --angle: 360deg;
  }
}
.purchase {
  display: flex;
  flex-direction: column;
  height: 100%;
  font-family: 'DouyinSansBold', sans-serif;
  //  background: url('@/assets/family/images/family-bg.svg') no-repeat center center; // 新增背景图
  // background-size: cover; // 保持背景图覆盖
  .top {
    height: 80%;
    width: 100%;
    position: relative;
    overflow: hidden;
    .logo {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      z-index: 30;
      .img-logo {
        width: 100%;
      }
    }
    .my-img {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%; // 修改为100%填充
      height: 100%; // 修改为100%填充
      z-index: 20;
    }
    .inner-camera {
      position: absolute;
      width: 100%;
      height: 100%;
      left: 0%;
      top: 0%;
      z-index: 19;
      display: flex;
      justify-content: center;
      align-items: center;
      // video {
      //   // transform: scaleX(-1); /* 镜像翻转 */
      //   transform: rotate(90deg) scale(1.35);
      //   object-fit: cover; /* 填充容器 */
      //   background: #000; /* 黑底占位 */
      // }
      .video-wraper {
        position: absolute;
        width: 100%;
        height: 80%;
        .preview-image {
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
          height: 100%;
          object-fit: cover;
          z-index: 1;
          transform: scaleX(-1) rotate(90deg) scale(1.35);
        }
      }
    }
    .count-numer {
      position: absolute;
      width: 100%;
      height: 100%;
      left: 0;
      top: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 507rem;
      color: #fff;
      font-weight: 700;
      z-index: 21;
    }
    // .guang-inner {
    //   position: absolute;
    //   width: 100%;
    //   height: 100%;
    //   left: 0;
    //   top: 0;
    //   display: flex;
    //   justify-content: center;
    //   align-items: center;
    //   z-index: 999;
    //   .guang-box {
    //     width: 260px;
    //     height: 260px;
    //     filter: drop-shadow(0 0 5px hsl(162, 100%, 58%)) drop-shadow(0 0 10px hsl(270, 73%, 53%));
    //     .guang {
    //       width: 260px;
    //       height: 260px;
    //       border-radius: 10px;
    //       clip-path: polygon(
    //         0% 0%,
    //         /* 外框左上 */ 100% 0%,
    //         /* 外框右上 */ 100% 100%,
    //         /* 外框右下 */ 0% 100%,
    //         /* 外框左下 */ 0% 0%,

    //         /* 闭合外框 */ /* 内框裁剪（从10%到90%） */ 5% 5%,
    //         /* 内框左上 */ 5% 95%,
    //         /* 内框左下 */ 95% 95%,
    //         /* 内框右下 */ 95% 5%,
    //         /* 内框右上 */ 5% 5% /* 闭合内框 */
    //       );
    //       background: conic-gradient(
    //         from var(--angle),
    //         hsl(162, 100%, 58%),
    //         hsl(270, 73%, 53%),
    //         hsl(162, 100%, 58%)
    //       );
    //       animation: rotate 3s infinite linear;
    //     }
    //   }
    // }
  }
}

.leftButton {
  position: relative;
  display: inline-block;
  .hotzone {
    position: absolute;
    width: 50%;
    height: 50%;
    pointer-events: auto;
    z-index: 21;
    // 可选：调试时显示颜色
    // background: rgba(255,0,0,0.2);
  }
  .top-left {
    top: 0;
    left: 0;
    // clip-path: polygon(0 0, 100% 0, 0 100%);
  }
  .top-right {
    top: 0;
    right: 0;
    // clip-path: polygon(100% 0, 100% 100%, 0 0);
  }
  .bottom-left {
    bottom: 0;
    left: 0;
    // clip-path: polygon(0 100%, 100% 100%, 0 0);
  }
  .bottom-right {
    bottom: 0;
    right: 0;
    // clip-path: polygon(100% 100%, 0 100%, 100% 0);
  }
  .hotArea {
    height: 100%;
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 20;
    transform: rotate(-45deg);
  }
}

.bk {
  position: fixed;
  width: 100vw;
  height: 10vh;
  top: 0;
  left: 0;
  z-index: 1;

  // transform: scale(1.5) translate(-100%, -30%);
  transform-origin: center;
  overflow: hidden;
  img {
    width: 100%;
  }
}
</style>
