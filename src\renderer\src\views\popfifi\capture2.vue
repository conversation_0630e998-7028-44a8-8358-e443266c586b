<template>
  <div class="capture">
    <img class="titlebk" src="@/assets/images/popofifititle.png">
    <video class="snapVideo" ref="snapVideo" autoplay playsinline></video>
    <div class="devBox">
      <video class="real" ref="realVideo" autoplay playsinline></video>
      <canvas class="real" ref="realCanvas"></canvas>
    </div>
    
    <button @click="toggleSnapVideo">{{ snapPlaying ? '暂停Snap' : '播放Snap' }}</button>
    <input type="file" accept="image/*" @change="onImageUpload" />
    <div class="canvas-group">
      <canvas class="snapCanvas" ref="snapCanvas"></canvas>
      
    </div>

    <div class="btnGroup">
      <!-- <template v-if="false">
        <div class="loading-text">
          正在打开摄像头<span class="dots">...</span>
        </div>
        <label class="btnUploadInput">
          上传本地图片
          <input type="file" accept="image/*" @change="handleFileUpload" hidden />
        </label>
      </template> -->
      
      <div v-if="!showButtons">
        <div class="btnCapture" @click="toggleSnapVideo('pai')">拍照</div>
        <label class="btnUploadInput">
          上传本地图片
          <input type="file" accept="image/*" @change="onImageUpload" hidden />
        </label>
      </div>
    </div>

    <!-- 提示和按钮 -->
    <div class="title" v-if="!showButtons">请保持脸部在拍照框内</div>
    <div class="btnWarp" v-if="showButtons">
      <div class="btnConfirm" @click="upload()">确认继续</div>
      <div class="btnRetake" @click="toggleSnapVideo('retake')">重拍一张</div>
    </div>
    <div class="btn" @click="back()">返回</div>
    <!-- <div class="btn2" @click="toKatong()">卡通</div> -->
    <div v-if="showError" class="error-toast">
      {{ errorMessage }}
    </div>
    <div class="user-ag-checkbox" v-if="!showUserAg">
      <input type="checkbox" class="checkbox" v-model="checked" />
      <span  @click="showUserAg = true">用户服务和隐私授权协议</span>
    </div>
    <div class="user-agreement" v-if="showUserAg">
      <div class="content">
      <div class="head">POPOFIFI用户协议</div>

      <div>欢迎您使用POPOFIFI专属卡通定制服务！</div>
      <div>在您使用本服务前，POPOFIFI 开发者北京天翔睿翼科技有限公司向您郑重做如下告知：</div>
      <div class="label">基础提示</div>
      <div>在使用POPOFIFI为您提供的专属卡通定制服务之前，请您务必审慎阅读、充分理解本告知(如果您未满16周岁，或已满16周岁未满18周岁且不能以自己的劳动收入作为主要收入来源的，请在法定监护人的陪同下阅读本告知）各项条款，特别是限制或免除责任条款、隐私保护条款等，以加粗加黑和/或加下划线等显示形式提示您注意的重要条款，请务必重点查阅。

       </div>

      <div>
      若您不同意本告知，则您有充分且完全的权利退出POPOFIFI专属卡通定制服务，您实际使用POPOFIFI专属卡通定制服务的行为即视为您已阅读、理解并同意接受本告知。如果您对本告知有任何的疑问、投诉、意见和建议，欢迎您通过本告知所附联系方式与我们沟通反馈。
      </div>
      <div class="label">服务内容</div>

      POPOFIFI为您提供丰富的服务，用户只需简单操作，即可快速捕捉面部图像，依托前沿AI智能生成技术，瞬间生成特色主题卡通照片并进一步定制成别具一格的潮流玩具、小挂件等。
      您同意并知悉，POPOFIFI的具体服务内容、功能和形式依据实际情况的不同按实际可见的状态提供，我们有权自行确定POPOFIFI服务的具体内容、功能和形式，有权自行决定增加、变更、中断和停止POPOFIFI具体的内容、功能和形式。具体以旅拍机实时呈现的服务内容、功能和形式为准。

      <div class="label">信息适用与存储豁免</div>
      当您使用POPOFIFI拍照或上传照片时，我们会收集您拍摄的照片信息。这些信息会通过AI智能生成技术，用于生成特色的卡通形象。同时为您提供更完美的服务体验，该信息也将会纳入我们的数据库，我们会进行科学管理。
      <div class="bold">对于收集到的照片信息，我们会先进行匿名化处理，将其转化为无法识别您个人身份的形式之后再以数据的形式加以存储，存储过程中我们始终严格遵循隐私保护原则，不遗余力地保障您的信息安全。
      </div>
      <div class="bold">对于生成的卡通形象，除另行约定之外，我们有权在保护您的身份与隐私的前提下，用于线上宣传与展示：抖音/小红书等平台推广；线下宣传与展示：门店陈列、展会灯箱；衍生创作：允许AI生成二次设计稿、视频、2D和3D数字人等。
      </div>
      <div>当您使用POPOFIFI进行拍照操作时，即代表您认可并同意我们有权依据此信息保护规则，对与您的照片等信息进行使用与存储。考虑到数据库优化的实际需求，就信息数据的存储事项，您同意豁免我们另行单独征得您的授权。
      </div>
      <div>如何联系我们</div>
      <div>如果您有任何的疑问、投诉、意见和建议，欢迎您与我们沟通反馈。我们的联系方式见下：</div>

        <div class="foot">客服热线： 010-88738088 </div>
        <div class="foot">客服邮箱： <EMAIL> </div>
        <div class="foot">日期： 2025 年 05 月 01 日</div>
      </div>
      <div class="footer">
        <button @click="showUserAg = false">我以知悉</button>
      </div>


    </div>
  </div>
</template>

<script>

import {ElMessage} from 'element-plus'
export default {
  data() {
    return {
      
      showButtons: false, // 是否显示拍照按钮
      isUpImg: false, // 是否上传图片
      snapPlaying: true,
      snapStream: null,
      realStream: null,
      capturedImage: null, // 用于存储生成的图片数据
      showUserAg: true, // 是否显示用户协议
      checked: true,
    };
  },
  mounted() {
    this.initCameras();
    // this.loadFaceApiModels();
  },
  methods: {
    upload() {
      let uploadFiles = (subjectFile)=>{
        this.$router.push({
          path: "/rebuild2-themeSelect",
          state: {
            subjectFile
          }
        });
      }
      if (this.capturedImage === null) {
        setTimeout(() => {
          if (!this.capturedImage){
            ElMessage({
              message: '没有识别出面部，请从新拍摄',
              type: 'error',
            });
            this.toggleSnapVideo('retake')
            return 
          }
          uploadFiles(this.capturedImage[0])
        }, 1000);
        return
      }else{
        uploadFiles(this.capturedImage[0])
      }
    
      
      
    },
    back() {
      this.$router.push({ path: '/' });
    },
    async initCameras() {
      const devices = await navigator.mediaDevices.enumerateDevices();
      const videoInputs = devices.filter(d => d.kind === 'videoinput');
      const snapCam = videoInputs.find(d => d.label.includes('Snap'));
      const otherCam = videoInputs.find(d => !d.label.includes('Snap'));

      if (snapCam) {
        navigator.mediaDevices.getUserMedia({ video: { deviceId: snapCam.deviceId } })
          .then(stream => {
            this.snapStream = stream;
            this.$refs.snapVideo.srcObject = stream;
          });
      }
      if (otherCam) {
        navigator.mediaDevices.getUserMedia({ video: { deviceId: otherCam.deviceId } })
          .then(stream => {
            this.realStream = stream;
            this.$refs.realVideo.srcObject = stream;
          });
      }
    },
    async loadFaceApiModels() {
      // 加载 face-api.js 模型
      const MODEL_URL = '/faceapi'; // 请确保模型文件夹路径正确
      await faceapi.nets.ssdMobilenetv1.loadFromUri(MODEL_URL);
      await faceapi.nets.faceLandmark68Net.loadFromUri(MODEL_URL);
      await faceapi.nets.faceRecognitionNet.loadFromUri(MODEL_URL);
    },
    toggleSnapVideo(state) {
      if (!this.checked){
        return ElMessage({
          message: '请同意用户服务和隐私授权协议',
          type: 'error',
        });
      }
      const snapVideo = this.$refs.snapVideo;
      if (state === 'pai') {
        this.showButtons = !this.showButtons;
        // 如果是拍照状态，暂停snap视频并显示当前帧
        this.snapPlaying = false;
        this.showsnapCanvas();
        // 捕获realVideo当前帧
        const snapVideo = this.$refs.snapVideo;
        snapVideo.pause();
        const realVideo = this.$refs.realVideo;
        const realCanvas = this.$refs.realCanvas;
        // 旋转后宽高互换
        realCanvas.width = realVideo.videoHeight;
        realCanvas.height = realVideo.videoWidth;
        const ctx = realCanvas.getContext('2d');
        ctx.save();
        // 顺时针旋转90度
        ctx.translate(realCanvas.width, 0);
        ctx.rotate(Math.PI / 2);
        ctx.drawImage(realVideo, 0, 0, realVideo.videoWidth, realVideo.videoHeight);
        ctx.restore();
        this.capturerealVideoFrame();
      } else {
        const snapVideo = this.$refs.snapVideo;
        snapVideo.play();
        this.capturedImage = null
        this.showButtons = !this.showButtons;
        // 播放snap视频
        // snapVideo.play();
        this.snapPlaying = true;
        this.clearsnapCanvas();
      }
    },
    async showsnapCanvas() {
      // 暂停时将snapVideo当前帧画到snapCanvas，并顺时针旋转90度
      const snapVideo = this.$refs.snapVideo;
      const snapCanvas = this.$refs.snapCanvas;
      // 旋转后宽高互换
      snapCanvas.width = snapVideo.videoHeight;
      snapCanvas.height = snapVideo.videoWidth;
      const ctx = snapCanvas.getContext('2d');
      ctx.save();
      ctx.translate(snapCanvas.width, 0);
      ctx.rotate(Math.PI / 2);
      ctx.drawImage(snapVideo, 0, 0, snapVideo.videoWidth, snapVideo.videoHeight);
      ctx.restore();
    },
    clearsnapCanvas() {
      const snapCanvas = this.$refs.snapCanvas;
      const ctx = snapCanvas.getContext('2d');
      ctx.clearRect(0, 0, snapCanvas.width, snapCanvas.height);

      const realCanvas = this.$refs.realCanvas;
      const ctxReal = realCanvas.getContext('2d');
      ctxReal.clearRect(0, 0, realCanvas.width, realCanvas.height);
    },
    async capturerealVideoFrame() {
      const realVideo = this.$refs.realVideo;
      const realCanvas = this.$refs.realCanvas;
      // 使用 face-api.js 检测面部
      const displaySize = { width: realCanvas.width, height: realCanvas.height };
      const detections = await faceapi.detectAllFaces(
        realCanvas,
        new faceapi.SsdMobilenetv1Options({ 
          minConfidence: 0.2,
          maxResults: 5
        })
      ).withFaceLandmarks();

      const resizedDetections = faceapi.resizeResults(detections, displaySize);
      // faceapi.draw.drawDetections(realCanvas, resizedDetections);
      // 如果检测到人脸，扩大一倍范围截图并保存
      if (resizedDetections.length > 0) {
        const fileList = [];
        for (let i = 0; i < resizedDetections.length; i++) {
          const box = resizedDetections[i].detection.box;
          // 扩大一倍范围
          const centerX = box.x + box.width / 2;
          const centerY = box.y + box.height / 2;
          const newWidth = box.width * 2;
          const newHeight = box.height * 2;
          let sx = Math.round(centerX - newWidth / 2);
          let sy = Math.round(centerY - newHeight / 2);
          let sw = Math.round(newWidth);
          let sh = Math.round(newHeight);

          // 边界处理
          if (sx < 0) sx = 0;
          if (sy < 0) sy = 0;
          if (sx + sw > realCanvas.width) sw = realCanvas.width - sx;
          if (sy + sh > realCanvas.height) sh = realCanvas.height - sy;

          // 创建临时canvas用于保存截图
          const tempCanvas = document.createElement('canvas');
          tempCanvas.width = sw;
          tempCanvas.height = sh;
          const tempCtx = tempCanvas.getContext('2d');
          tempCtx.drawImage(realCanvas, sx, sy, sw, sh, 0, 0, sw, sh);

          // 保存图片
          await new Promise(resolve => {
            tempCanvas.toBlob(blob => {
              const file = new File([blob], `face_capture_${i + 1}.jpg`, { type: 'image/jpeg' });
              fileList.push(file);
              // const url = URL.createObjectURL(blob);
              // const a = document.createElement('a');
              // a.href = url;
              // a.download = file.name;
              // a.click();
              // URL.revokeObjectURL(url);
              resolve();
            }, 'image/jpeg');
          });
        }
        this.capturedImage = fileList; // 保存生成的图片数据
        // 打印file类型对象
        console.log('保存的文件对象:', fileList);
      }
    },
    async onImageUpload(e) {
      this.showButtons = !this.showButtons
      const file = e.target.files[0];
      if (!file) return;
      const img = new window.Image();
      img.onload = async () => {
        const realCanvas = this.$refs.realCanvas;
        realCanvas.width = img.naturalWidth;
        realCanvas.height = img.naturalHeight;
        const ctx = realCanvas.getContext('2d');
        ctx.clearRect(0, 0, realCanvas.width, realCanvas.height);
        ctx.drawImage(img, 0, 0, realCanvas.width, realCanvas.height);

        const snapCanvas = this.$refs.snapCanvas;
        snapCanvas.width = img.naturalWidth;
        snapCanvas.height = img.naturalHeight;
        const ctxSnap = snapCanvas.getContext('2d');
        ctxSnap.clearRect(0, 0, snapCanvas.width, snapCanvas.height);
        ctxSnap.drawImage(img, 0, 0, snapCanvas.width, snapCanvas.height);



        await this.capturerealVideoFrame();
      };
      img.src = URL.createObjectURL(file);
    }
  }
}
</script>
<style lang="less" scoped>

.devBox{
  position: absolute;
  top: 0;
  left: 0;
  overflow: hidden;
  z-index: 0;
  .real{
    background-color: #000;
    height: 400px;
    width: 400px;
  }
}
canvas {
  border: none;
}
.snapVideo, .snapCanvas{
  
  object-fit: cover;
  
  position: absolute;
  top: 50%;
  left: 50%;
}
.snapVideo {
  transform: translate(-50%,-50%) rotate(90deg);
  height: 100vw!important;
  width: 100vh!important;
  z-index: 1;
}
.snapCanvas{
  transform: translate(-50%,-50%);
  height: 100vh!important;
  width: 100vw!important;
  z-index: 2;
}

.title {
  position: absolute;
  top: 77%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80vw;
  font-size: 4vw;
  font-weight: 700;
  color: rgba(230, 230, 230, 1);
  font-family: "萍方 繁 正规体", sans-serif;
  z-index: 9;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  white-space: nowrap;
}

.btnWarp {
  z-index: 9;
  position: absolute;
  top: 82%;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 96rem;
}

.btnConfirm {
  width: 520rem;
  height: 120rem;
  line-height: 120rem;
  text-align: center;
  background: url("@/assets/home/<USER>");
  background-size: 100% 100%;
  font-size: 48rem;
  font-weight: bold;
  color: #ffffff;
  font-family: "思源黑体";
  border: none;
}

.btnRetake {
  width: 520rem;
  height: 120rem;
  line-height: 120rem;
  text-align: center;
  background: transparent;
  border: 4rem solid rgba(255, 255, 255, 0.5);
  font-size: 48rem;
  color: #ffffff;
  font-family: "思源黑体";
  font-weight: bold;
  border-radius: 999rem;
  box-sizing: border-box;
}



.btn, .btn2 {
  position: absolute;
  z-index: 9;
  top: 320rem;
  left: 60rem;
  width: 320rem;
  height: 120rem;
  line-height: 120rem;
  text-align: center;
  background: transparent;
  border: 4rem solid rgba(255, 255, 255, 0.5);
  font-size: 48rem;
  color: #ffffff;
  font-family: "思源黑体";
  font-weight: bold;
  border-radius: 999rem;
  box-sizing: border-box;
}
.btnGroup {
  position: absolute;
  top: 82%;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 55rem;
  z-index: 10;

  .btnCapture {
    width: 520rem;
    height: 120rem;
    line-height: 120rem;
    text-align: center;
    background: rgba(255, 255, 255, 0.9);
    font-size: 48rem;
    font-weight: bold;
    color: #000000;
    border-radius: 999rem;
    font-family: "思源黑体";
    margin-bottom: 55rem;
  }

  .btnUploadInput {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 520rem;
    height: 120rem;
    background: transparent;
    border: 4rem solid rgba(255, 255, 255, 0.5);
    font-size: 48rem;
    color: #ffffff;
    font-family: "思源黑体";
    font-weight: bold;
    border-radius: 999rem;
    box-sizing: border-box;
    cursor: pointer;
  }

  .loading-text {
    font-size: 48rem;
    font-weight: bold;
    color: #ffffff;
    font-family: "思源黑体";
    text-align: center;
  }

  .dots::after {
    content: '';
    animation: blinkDots 1s steps(3, end) infinite;
  }

  @keyframes blinkDots {
    0%   { content: ''; }
    33%  { content: '.'; }
    66%  { content: '..'; }
    100% { content: '...'; }
  }

}
.titlebk{
  // height: 200rem
  width: 90vw;
  position:absolute;
  z-index: 555;
  margin-top: 100rem;
  left: 5vw;
}
.capture {
  overflow: hidden;
  video {
    width: 320px;
    height: 240px;
    background: #000;
  }
  button {
    height: 40px;
    margin-left: 16px;
  }
  .canvas-group {
    // display: flex;
    // flex-direction: column;
    // gap: 8px;
    overflow: hidden;
    height: 100vh;
    width: 100vw;
    position: absolute;
    top: 0;
    left: 0;
    canvas {
      width: 300px;
      height: 533px;
      border: 1px solid #ccc;
      background: #ffffff00;
    }
  }
}
</style>
