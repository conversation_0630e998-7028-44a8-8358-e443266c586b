<template>
  <div class="update-checker">
    <!-- 更新检查状态显示 -->
    <div v-if="showUpdateInfo" class="update-info">
      <div class="version-comparison">
        <div class="current-version">
          <h4>当前版本</h4>
          <div class="version-details">
            <div class="version-number">{{ currentVersion }}</div>
            <div class="build-info">
              <span>构建时间: {{ formatBuildTime(currentVersionInfo[3]) }}</span>
              <span v-if="currentVersionInfo[4]">Git: {{ currentVersionInfo[4] }}</span>
            </div>
          </div>
        </div>
        
        <div class="version-arrow">→</div>
        
        <div class="latest-version">
          <h4>最新版本</h4>
          <div class="version-details">
            <div class="version-number" :class="{ 'has-update': hasUpdate }">
              {{ latestVersion || '检查中...' }}
            </div>
            <div v-if="latestVersionInfo" class="build-info">
              <span>发布时间: {{ formatReleaseTime(latestVersionInfo.created_at) }}</span>
              <span v-if="latestVersionInfo.status">状态: {{ latestVersionInfo.status }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 更新状态和操作 -->
      <div class="update-actions">
        <div v-if="checking" class="status checking">
          <el-icon class="is-loading"><Loading /></el-icon>
          正在检查更新...
        </div>
        
        <div v-else-if="hasUpdate" class="status has-update">
          <el-icon><SuccessFilled /></el-icon>
          发现新版本！
          <div class="action-buttons">
            <el-button type="primary" @click="downloadUpdate" :loading="downloading">
              {{ downloading ? '下载中...' : '立即更新' }}
            </el-button>
            <el-button @click="skipUpdate">跳过</el-button>
          </div>
        </div>
        
        <div v-else-if="!checking" class="status up-to-date">
          <el-icon><CircleCheckFilled /></el-icon>
          已是最新版本
        </div>

        <div v-if="error" class="status error">
          <el-icon><WarningFilled /></el-icon>
          {{ error }}
          <el-button size="small" @click="checkForUpdates">重试</el-button>
        </div>
      </div>

      <!-- 下载进度 -->
      <div v-if="downloading && downloadProgress > 0" class="download-progress">
        <el-progress :percentage="downloadProgress" :show-text="true" />
        <div class="progress-info">
          {{ formatBytes(downloadedBytes) }} / {{ formatBytes(totalBytes) }}
        </div>
      </div>
    </div>

    <!-- 简化显示模式 -->
    <div v-else class="simple-update-check">
      <el-button 
        @click="checkForUpdates" 
        :loading="checking"
        size="small"
        type="text"
      >
        <el-icon><Refresh /></el-icon>
        {{ checking ? '检查中...' : '检查更新' }}
      </el-button>
    </div>
  </div>
</template>

<script>
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Loading, 
  SuccessFilled, 
  CircleCheckFilled, 
  WarningFilled, 
  Refresh 
} from '@element-plus/icons-vue'
import axios from 'axios'

export default {
  name: 'UpdateChecker',
  components: {
    Loading,
    SuccessFilled,
    CircleCheckFilled,
    WarningFilled,
    Refresh
  },
  props: {
    // 更新服务器地址
    updateServerUrl: {
      type: String,
      default: 'http://localhost:8080'
    },
    // 是否自动检查更新
    autoCheck: {
      type: Boolean,
      default: true
    },
    // 是否显示详细信息
    showDetails: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      currentVersion: '',
      currentVersionInfo: [],
      latestVersion: '',
      latestVersionInfo: null,
      hasUpdate: false,
      checking: false,
      downloading: false,
      downloadProgress: 0,
      downloadedBytes: 0,
      totalBytes: 0,
      error: '',
      showUpdateInfo: false
    }
  },
  async mounted() {
    await this.loadCurrentVersion()
    if (this.autoCheck) {
      await this.checkForUpdates()
    }
  },
  methods: {
    async loadCurrentVersion() {
      try {
        if (window.api) {
          this.currentVersion = await window.api.getVersion()
          this.currentVersionInfo = await window.api.getVersionInfo()
        } else {
          // 开发环境fallback
          this.currentVersion = '1.0.0'
          this.currentVersionInfo = ['1', '0', '0', '250722000000', 'dev']
        }
      } catch (error) {
        console.error('获取当前版本失败:', error)
        this.error = '获取当前版本失败'
      }
    },

    async checkForUpdates() {
      this.checking = true
      this.error = ''
      this.showUpdateInfo = this.showDetails
      
      try {
        // 调用更新服务API获取最新版本信息
        const response = await axios.get(`${this.updateServerUrl}/api/versions`)
        const versions = response.data.data
        
        if (versions && versions.length > 0) {
          // 找到最新的激活版本
          const activeVersions = versions.filter(v => v.status === 'active')
          if (activeVersions.length > 0) {
            const latest = activeVersions[0] // 假设第一个是最新的
            this.latestVersion = latest.version
            this.latestVersionInfo = latest
            
            // 比较版本号
            this.hasUpdate = this.compareVersions(this.currentVersion, this.latestVersion) < 0
          } else {
            this.error = '没有找到可用的更新版本'
          }
        } else {
          this.error = '无法获取版本信息'
        }
      } catch (error) {
        console.error('检查更新失败:', error)
        this.error = '检查更新失败，请检查网络连接'
      } finally {
        this.checking = false
      }
    },

    async downloadUpdate() {
      if (!this.latestVersionInfo) return
      
      try {
        const result = await ElMessageBox.confirm(
          `确定要更新到版本 ${this.latestVersion} 吗？更新完成后程序将自动重启。`,
          '确认更新',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'info'
          }
        )
        
        if (result === 'confirm') {
          this.downloading = true
          this.downloadProgress = 0
          
          // 下载更新文件
          const downloadUrl = `${this.updateServerUrl}/api/versions/${this.latestVersion}/download`

          // 实际下载更新文件
          await this.downloadUpdateFile(downloadUrl)
          
          ElMessage.success('更新下载完成，程序将重启')
          
          // 重启应用
          setTimeout(() => {
            if (window.api && window.api.restartApp) {
              window.api.restartApp()
            } else {
              location.reload()
            }
          }, 2000)
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('下载更新失败:', error)
          ElMessage.error('下载更新失败')
        }
      } finally {
        this.downloading = false
      }
    },

    async downloadUpdateFile(downloadUrl) {
      try {
        // 使用fetch下载文件，支持进度监控
        const response = await fetch(downloadUrl)

        if (!response.ok) {
          throw new Error(`下载失败: ${response.statusText}`)
        }

        const contentLength = response.headers.get('content-length')
        this.totalBytes = contentLength ? parseInt(contentLength) : 0
        this.downloadedBytes = 0
        this.downloadProgress = 0

        const reader = response.body.getReader()
        const chunks = []

        while (true) {
          const { done, value } = await reader.read()

          if (done) break

          chunks.push(value)
          this.downloadedBytes += value.length

          if (this.totalBytes > 0) {
            this.downloadProgress = Math.round((this.downloadedBytes / this.totalBytes) * 100)
          }
        }

        // 将下载的数据转换为Blob
        const blob = new Blob(chunks)

        // 创建下载链接并触发下载
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `update-${this.latestVersion}.exe`
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        window.URL.revokeObjectURL(url)

      } catch (error) {
        console.error('下载更新文件失败:', error)
        throw error
      }
    },

    skipUpdate() {
      this.showUpdateInfo = false
      ElMessage.info('已跳过此次更新')
    },

    compareVersions(version1, version2) {
      const v1parts = version1.split('.').map(Number)
      const v2parts = version2.split('.').map(Number)
      
      for (let i = 0; i < Math.max(v1parts.length, v2parts.length); i++) {
        const v1part = v1parts[i] || 0
        const v2part = v2parts[i] || 0
        
        if (v1part < v2part) return -1
        if (v1part > v2part) return 1
      }
      
      return 0
    },

    formatBuildTime(timestamp) {
      if (!timestamp || timestamp.length !== 12) return '未知'
      
      const year = '20' + timestamp.substr(0, 2)
      const month = timestamp.substr(2, 2)
      const day = timestamp.substr(4, 2)
      const hour = timestamp.substr(6, 2)
      const minute = timestamp.substr(8, 2)
      const second = timestamp.substr(10, 2)
      
      return `${year}-${month}-${day} ${hour}:${minute}:${second}`
    },

    formatReleaseTime(dateString) {
      if (!dateString) return '未知'
      return new Date(dateString).toLocaleString('zh-CN')
    },

    formatBytes(bytes) {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }
  }
}
</script>

<style scoped>
.update-checker {
  padding: 16px;
  border-radius: 8px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
}

.version-comparison {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 16px;
}

.current-version,
.latest-version {
  flex: 1;
  text-align: center;
}

.current-version h4,
.latest-version h4 {
  margin: 0 0 8px 0;
  color: #666;
  font-size: 14px;
}

.version-details {
  padding: 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #ddd;
}

.version-number {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.version-number.has-update {
  color: #409eff;
}

.build-info {
  font-size: 12px;
  color: #999;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.version-arrow {
  font-size: 24px;
  color: #999;
  font-weight: bold;
}

.update-actions {
  text-align: center;
}

.status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px;
  border-radius: 6px;
  font-size: 14px;
}

.status.checking {
  background: #e6f7ff;
  color: #1890ff;
}

.status.has-update {
  background: #f6ffed;
  color: #52c41a;
  flex-direction: column;
  gap: 12px;
}

.status.up-to-date {
  background: #f6ffed;
  color: #52c41a;
}

.status.error {
  background: #fff2f0;
  color: #ff4d4f;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.download-progress {
  margin-top: 16px;
}

.progress-info {
  text-align: center;
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

.simple-update-check {
  text-align: center;
}
</style>
