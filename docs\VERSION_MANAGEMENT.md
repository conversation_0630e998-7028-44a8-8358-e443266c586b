# 版本管理系统

本项目实现了自动版本管理系统，每次构建时会自动递增第三个版本号（补丁版本号），并在运行时提供版本信息访问。

## 功能特性

### 1. 自动版本递增
- 每次执行 `npm run build` 时，会自动将版本号的第三位数字加1
- 例如：`1.0.0` → `1.0.1` → `1.0.2`

### 2. 运行时版本信息
- 应用运行时可以获取当前版本号
- 提供完整的版本信息数组，包括：
  - 主版本号
  - 次版本号  
  - 补丁版本号
  - 构建时间戳
  - Git提交哈希

## 使用方法

### 在主进程中使用

```typescript
import { getCurrentVersion, getVersions } from './getVersion'

// 获取当前版本号字符串
const version = getCurrentVersion() // "1.0.1"

// 获取完整版本信息数组
const versionInfo = getVersions() // ["1", "0", "1", "250118123045", "abc1234"]
```

### 在渲染进程中使用

```javascript
// 获取当前版本号
const version = await window.api.getVersion()

// 获取完整版本信息
const versionInfo = await window.api.getVersionInfo()
```

### 在Vue组件中使用

```vue
<template>
  <div>
    <p>当前版本: {{ version }}</p>
    <p>构建时间: {{ buildTime }}</p>
  </div>
</template>

<script>
export default {
  data() {
    return {
      version: '',
      buildTime: ''
    }
  },
  async mounted() {
    this.version = await window.api.getVersion()
    const versionInfo = await window.api.getVersionInfo()
    this.buildTime = versionInfo[3] // 构建时间戳
  }
}
</script>
```

## 构建脚本

### 自动构建
正常使用构建命令，版本会自动递增：

```bash
npm run build              # 自动递增版本并构建
npm run build:win         # 自动递增版本并构建Windows版本
npm run build:win:test    # 自动递增版本并构建测试版本
npm run build:win:prod    # 自动递增版本并构建生产版本
```

### 手动递增版本
如果需要手动递增版本号：

```bash
node scripts/increment-version.js
```

## 文件结构

```
├── scripts/
│   └── increment-version.js    # 版本递增脚本
├── src/
│   ├── main/
│   │   └── getVersion.ts       # 版本管理模块
│   ├── preload/
│   │   └── index.ts           # 预加载脚本（包含版本API）
│   └── renderer/
│       └── src/
│           └── components/
│               └── VersionInfo.vue  # 版本信息组件示例
└── package.json               # 版本号存储位置
```

## API 参考

### 主进程 API

#### `getCurrentVersion(): string`
返回当前版本号字符串，如 "1.0.1"

#### `getVersions(): string[]`
返回完整版本信息数组：
- `[0]`: 主版本号
- `[1]`: 次版本号
- `[2]`: 补丁版本号
- `[3]`: 构建时间戳 (YYMMDDhhmmss格式)
- `[4]`: Git提交哈希 (短格式)

#### `updatePackageVersion(newVersion: string): void`
更新package.json中的版本号

#### `incrementAndUpdateVersion(): string`
自动递增版本号并更新package.json，返回新版本号

### 渲染进程 API

#### `window.api.getVersion(): Promise<string>`
获取当前版本号

#### `window.api.getVersionInfo(): Promise<string[]>`
获取完整版本信息数组

## 注意事项

1. **构建前自动递增**: 每次运行构建命令时，版本号会在构建开始前自动递增
2. **Git依赖**: Git哈希获取依赖于Git环境，如果不在Git仓库中运行，该字段将为空
3. **时间格式**: 构建时间使用 YYMMDDhhmmss 格式，如 "250118123045" 表示 2025年1月18日12:30:45
4. **版本持久化**: 版本号变更会直接写入package.json文件，确保版本信息的持久化

## 示例组件

项目中包含了一个示例组件 `VersionInfo.vue`，展示了如何在Vue组件中使用版本信息。你可以在任何页面中导入并使用这个组件来显示版本信息。
