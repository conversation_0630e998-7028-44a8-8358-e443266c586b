# kadayapkge

An Electron application with Vue and TypeScript

## Recommended IDE Setup

- [VSCode](https://code.visualstudio.com/) + [ESLint](https://marketplace.visualstudio.com/items?itemName=dbaeumer.vscode-eslint) + [Prettier](https://marketplace.visualstudio.com/items?itemName=esbenp.prettier-vscode) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) + [TypeScript Vue Plugin (Volar)](https://marketplace.visualstudio.com/items?itemName=Vue.vscode-typescript-vue-plugin)

## Project Setup

### Install

```bash
$ npm install
```

### Development

```bash
$ npm run dev
```

### Build

```bash
# For windows
$ npm run build:win

# For macOS
$ npm run build:mac

# For Linux
$ npm run build:linux
```

### Version Management

This project includes automatic version management:

- **Auto-increment**: Each build automatically increments the patch version number (third digit)
- **Runtime access**: Get version info in your application using `window.api.getVersion()` and `window.api.getVersionInfo()`
- **Build info**: Includes build timestamp and git commit hash

See [docs/VERSION_MANAGEMENT.md](docs/VERSION_MANAGEMENT.md) for detailed usage instructions.
npm run typecheck &&