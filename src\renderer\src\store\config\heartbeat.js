import axios from 'axios'


let timeOut = null

export let startHeart = e=>{
    
    let getConf = e=>{
        axios.get('/api/v1.0/device/keepalive', {
            params:{
                app_version: window.process.env.npm_package_version
            }
        }).then(e=>{
            timeOut = setTimeout(() => {
                getConf()
            }, 60*1000)
            
        })
    }
    getConf()
}

export let endHeart = e=>{

}