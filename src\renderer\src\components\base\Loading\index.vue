<template>
  <div :class="['loader',{
    'init-loader':myProps.isInitBoxWidthAndHeight
  }]" >
    <div v-for="dot in 5" :key="dot" class="dot" :style="{
        width: `${size}`,
        height: `${size}`,
    }"></div>
  </div>
</template>

<script setup>
 const myProps =  defineProps({
    size: {
      type: String,
      default: '30rem'
    },
    isInitBoxWidthAndHeight:{
      type:Boolean,
      default:false
    }
 })
</script>

<style lang="less" scoped>
@keyframes slide {
  0% {
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(2);
  }
  100% {
    transform: scale(1);
  }
}
.loader {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  .dot {
    background: #3ac;
    border-radius: 100%;
    display: inline-block;
    animation: slide 1s infinite;
    &:nth-child(1) {
      animation-delay: 0.1s;
      background: #32aacc;
    }
    &:nth-child(2) {
      animation-delay: 0.2s;
      background: #64aacc;
    }
    &:nth-child(3) {
      animation-delay: 0.3s;
      background: #96aacc;
    }
    &:nth-child(4) {
      animation-delay: 0.4s;
      background: #c8aacc;
    }
    &:nth-child(5) {
      animation-delay: 0.5s;
      background: #faaacc;
    }
  }
}
.init-loader {
  width: initial;
  height: initial;
}
</style>
