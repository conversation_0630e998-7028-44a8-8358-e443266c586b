import {ref} from 'vue'
import { defineStore } from 'pinia'
import { ElMessage } from 'element-plus'
// camera
// 初始化camera 获取视频流
// snap效果的数据流
const cameraSnapStream = ref(null);

// 普通效果的数据流
const cameraCommonStream = ref(null);

// 加载
const loadSnapStream = async()=> {
      try {
        const devices = await navigator.mediaDevices.enumerateDevices()
        const snapCam = devices.find((d) => d.kind === 'videoinput' && d.label.includes('Snap'))
        console.log('访问到了么？',snapCam)
        if (snapCam) {
          const stream = await navigator.mediaDevices.getUserMedia({
            video: { 
              deviceId: { exact: snapCam.deviceId }, 
               advanced: [
               { width: 3840, height: 2160 }, // 优先尝试4K
               { width: 1920, height: 1080 }, // 其次1080P
               { width: 1280, height: 720 },  // 最后720P
              ]
            }
          })
           return stream
        }
      } catch (error) {
        console.error('摄像头访问失败:', error)
        ElMessage({
          message: '请检查摄像头权限和连接状态',
          type: 'error'
        })
      }
 }
const loadCommonStream = async()=>{
       try {
        const devices = await navigator.mediaDevices.enumerateDevices()
        const commonCam = devices.find((d) => d.kind === 'videoinput' && !d.label.includes('Snap'))
        if (commonCam) {
          const stream = await navigator.mediaDevices.getUserMedia({
            video: { 
              deviceId: { exact: commonCam.deviceId },  
              advanced: [
               { width: 3840, height: 2160 }, // 优先尝试4K
               { width: 1920, height: 1080 }, // 其次1080P
               { width: 1280, height: 720 },  // 最后720P
              ]
           }
          })
         return stream
        }
      } catch (error) {
        console.error('摄像头访问失败:', error)
        ElMessage({
          message: '请检查摄像头权限和连接状态',
          type: 'error'
        })
      }
}

const setSnapStream = (data) => {
    cameraSnapStream.value = data;
}
const setCommonStream = (data) => {
    cameraCommonStream.value = data;
}

// faceApi的实例化保存
export const useCameraStore = defineStore('cameraStore',()=>{
    return { cameraSnapStream,cameraCommonStream,setSnapStream,setCommonStream,loadSnapStream, loadCommonStream}
})