<template>
  <div class="home">

    <!-- 错误提示框 -->
    <div v-if="showError" class="error-toast">
      {{ errorMessage }}
    </div>


    <!-- 背景暗化层 -->
    <!-- <div class="background-overlay">
      
    </div> -->
    <img class="bk" src="@/assets/images/background.png">
    <!-- <img class="title" src="@/assets/images/popofifititle.png"> -->
       <div class="my-video-wraper">
        <BaseCommonLoopVideo :videoSrc="loadingVideo"  />
      <!-- <video src="@/assets/video/loading.mp4" autoplay loop muted></video> -->
    </div>
    <div class="progress-bar-wrapper">
      <div class="progress-bar-inner" :style="{ width: progress + '%' }"></div>
    </div>
    <div class="loading-text">
      正在进行元宇宙穿越
    </div>


  </div>
</template>

<script>
import { ElMessage } from 'element-plus'
import axios from 'axios'
import { useFamilyPhotoStore } from '@/store/family'
// 注册 Swiper 模块
// SwiperCore.use([Autoplay])
import {uploadImg} from '../uploadImg.js'
import testImg from '@/assets/test.jpg'
import BaseCommonLoopVideo from '@/components/base/CommonLoopVideo/index.vue'
import loadingVideo from '@/assets/video/loading_new_small.mp4'
export default {
  name: 'HomePage',
  components:{
    BaseCommonLoopVideo
  },
  data() {
    return {
      images: [
        new URL('@/assets/home/<USER>', import.meta.url),
        new URL('@/assets/home/<USER>', import.meta.url),
        new URL('@/assets/home/<USER>', import.meta.url),
      ],
      fgSwiper: null,
      progress: 0,
      showError: false,
      errorMessage: '',
      getRoundTimeout: null,
      timeout: null,
      loadingVideo
    }
  },
  mounted() {
    this.startProgress();
    // return
    
    this.generateAllTemplates();
  },
  unmounted() {
    this.getRoundTimeout && clearTimeout(this.getRoundTimeout)
    this.timeout && clearTimeout(this.timeout)
  },
  methods: {


    generatePrefix() {
      const now = new Date();
      const pad = (n) => n.toString().padStart(2, '0');

      const datePart = `${now.getFullYear()}${pad(now.getMonth() + 1)}${pad(now.getDate())}`;
      const timePart = `${pad(now.getHours())}${pad(now.getMinutes())}${pad(now.getSeconds())}`;
      const randPart = Math.floor(Math.random() * 1000); // 0~999 随机数
      return `test_${datePart}_${timePart}_${randPart}`;
    },
    getResult(taskId, pic){
      let f = () => {
        axios.get('/api/v1.0/digital-avatar/task/2d/get',{
          params:{
            "task_id": taskId,
            "client_id": window.clientId.screen_num
          }
        }).then(e=>{
          console.info('第一张：',new Date().getTime())
          console.log(e)
          let avatars = e.data.data.avatars
          let value = avatars.find(e=>{
            let v = e.styles.find(_e=>_e.status == 2)
            if (v){
              return v
            }
          })
          if (value){
            console.log(value)
            let v = value.styles.find(e=>e.status == 2)
            let img = new Image()
            img.onload = e=>{
              clearInterval(this.getInterval)
              this.$router.push({path: '/family-result',state: {
                taskId: taskId,
                pic:pic
              }})
            }
            img.src = v.result_url
          }else{
            this.getRoundTimeout = setTimeout(f,1000)
          }
          // if (e){
          //   clearInterval(this.getInterval)
          //   this.$router.push({
          //     path: "/rebuild2-result",
          //     state: {
          //       client_id: client_id,
          //       results: e.data.data,
          //       taskId: taskId,
          //       avatarId: r.avatarId,
          //       objectKey: r.objectKey,
          //       remainCodes: orderedTemplateCodes,
          //       code: templatecode,
          //       subjectFile,
          //       gender
          //     }
          //   })
          // }
        }).catch(err=>{
          if (err?.code === 4000){
            this.showErrorToast('获取结果失败：' + (err?.msg || '未知错误'))
            setTimeout(() => {
              this.showError = false
              this.$router.push({ path: '/welcome' })
            }, 3000)
          }
            
        })
      }
      f()
      
    },
    async generateAllTemplates() {
      const useFamilyStore = useFamilyPhotoStore()
      let familyPhoto = useFamilyStore.familyPhotoData
      let familyOriPic = useFamilyStore.familyOriPic

      let p_img = await uploadImg(familyOriPic.orifile)
      let promiseLst = []
      console.log(p_img)
      // return
      familyPhoto.forEach(e=>{
        promiseLst.push(uploadImg(e.thumbnailFile, p_img.avatarId))
      })
      Promise.all(promiseLst).then(async (imgDetails) => {
        console.log(imgDetails)
        let imgs = imgDetails
        // 抽签函数
        function getRandomNumbers(total, count) {
            // 创建一个从 0 到 total-1 的数组
            let numbers = Array.from({ length: total }, (_, i) => i );
            // 随机打乱数组顺序
            for (let i = numbers.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [numbers[i], numbers[j]] = [numbers[j], numbers[i]];
            }
            // 返回前 count 个数字
            return numbers.slice(0, count);
        }
        
        let mencodes = ['p_105', 'p_109', 'p_s001', 'p_s003', 'p_s004', 'p_s007', 'p_s017', 'p_s018' ]
        let womencodes = ['p_136', 'p_138', 'p_s014', 'p_s015', 'p_s016', 'p_s002', 'p_s005', 'p_s006', 'p_s008', 'p_s009', 'p_s011', 'p_s012', 'p_s019', 'p_s020']
        let radom1 = getRandomNumbers(mencodes.length,mencodes.length)
        // 男性不满10个，填补重复
        radom1.push(...getRandomNumbers(mencodes.length,2))
        let radom2 = getRandomNumbers(womencodes.length,womencodes.length)
        // 调用函数，从 10 个数字中随机选择 3 个
        // let result = getRandomNumbers(10, 3);
        // console.log(result);
        console.log(radom1)
        console.log(radom2)
        let data = imgs.map((item,index)=>{
          let styles = []
          // familyPhoto[index].ageType == 'big' && familyPhoto[index].gender == 'male' && styles.push(...['p_105', 'p_109'])
          // familyPhoto[index].ageType == 'big' && familyPhoto[index].gender == 'female' && styles.push(...['p_136', 'p_138'])
          // familyPhoto[index].ageType == 'small' && familyPhoto[index].gender == 'male' && styles.push(...['p_111', 'p_128'])
          // familyPhoto[index].ageType == 'small' && familyPhoto[index].gender == 'female' && styles.push(...['p_137', 'p_139'])
          familyPhoto[index].gender == 'male' && styles.push(mencodes[radom1[index*2]], mencodes[radom1[index*2 + 1]])
          familyPhoto[index].gender == 'female' && styles.push(womencodes[radom2[index*2]], womencodes[radom2[index*2 + 1]])
          return {
            index,
            avatar_image: item.objectKey,
            styles
          }
        })
        let taskId = new Date().getTime().toString()
        axios.post('/api/v1.0/digital-avatar/task/2d/gen',{
          client_id: window.clientId.screen_num,
          task_id: taskId,
          digital_avatar_id: p_img.avatarId,
          data
        }).then(res=>{
          this.timeout = setTimeout(e=>{
            this.getResult(res.data.data.task_id, p_img)
          }, 5000)
        }).catch(err=>{
          console.error('生成数字人失败:', err)
          ElMessage({
            message: '创建订单失败，请检查网络连接或重新拍摄',
            type: 'error'
          })
        })
      }).catch(err=>{
        if (err?.code === 4000){
          ElMessage({
            message: err?.msg ||'未知错误',
            type: 'error'
          })
          // this.showErrorToast('获取结果失败：' + (err?.msg || '未知错误'))
          setTimeout(() => {
            this.showError = false
            this.$router.push({ path: '/welcome' })
          }, 3000)
        }
        
        // console.error('转换图片到 File 对象失败:', err)
        // this.showErrorToast('转换图片到 File 对象失败: ' + err.message);
      })
      console.log(familyPhoto, familyOriPic)
      return

      const templatecode = history.state?.code|| '001'
      const gender = history.state?.gender || 'female';
      const refresh = history.state?.refresh ? "true" : "false";

      const templateMap = {
        female: ['003', '004', '006', '007', '009'], //009
        male: ['001', '002', '005','003', '008']
      };
      
      // 构造不重复的模板序列：先处理用户选择的，再处理性别对应的
      const templateSet = new Set(templateMap[gender]);
      templateSet.delete(templatecode); // 避免重复
      const orderedTemplateCodes = [templatecode, ...Array.from(templateSet)].slice(0, 5);
      const testImageFile = await this.getTestImageAsFile();
      subjectFile = subjectFile || testImageFile;

      let r = await uploadImg(subjectFile)
      console.log(r)
      
      let client_id= localStorage.getItem('clientId')
      if (r.objectKey){
        let taskId = new Date().getTime().toString()
        let res = await axios.post('/api/v1.0/digital-avatar/pre_gen_2d', {
          "client_id": window.clientId.screen_num,
          "task_id": taskId,
          "digital_avatar_id": r.avatarId,
          "style": orderedTemplateCodes.map(e=>{return 'p_'+e}),
          "avatar_image": r.objectKey
        })
        console.log(res)
        console.log(orderedTemplateCodes)
        setTimeout(() => {
        
        this.getInterval = setInterval(() => {
          axios.post('/api/v1.0/digital-avatar/get_gen_2d_result',{
            "task_id": taskId,
            "client_id": window.clientId.screen_num,
            "digital_avatar_id": r.avatarId
          }).then(e=>{
            console.info('第一张：',new Date().getTime())
            console.log(e)
            if (e){
              clearInterval(this.getInterval)
              this.$router.push({
                path: "/rebuild2-result",
                state: {
                  client_id: client_id,
                  results: e.data.data,
                  taskId: taskId,
                  avatarId: r.avatarId,
                  objectKey: r.objectKey,
                  remainCodes: orderedTemplateCodes,
                  code: templatecode,
                  subjectFile,
                  gender
                }
              })
            }
          }).catch(err=>{
            if (err?.code === 4000){
              this.showErrorToast('获取结果失败：' + (err?.msg || '未知错误'))
              setTimeout(() => {
                this.showError = false
                this.$router.push({ path: '/rebuild2-capture' })
              }, 3000)
            }
            
          })
        },1000)
      },5000)
      }
      
      return
      

      const resultList = [];

      for (let i = 0; i < orderedTemplateCodes.length; i++) {
        const code = orderedTemplateCodes[i];
        const result = await this.generateOneTemplate(subjectFile, code, refresh);

        if (i === 0 && !result) return
        if (result) resultList.push(result);

        if (i === 0 && result) {
          this.$router.push({
            path: "/rebuild2-result",
            state: {
              results: resultList,
              remainCodes: orderedTemplateCodes.slice(1),
              subjectFile,
              gender
            }
          });
        }
      }
    },
    async convertImageToFile(imageUrl, fileName = 'test.jpg') {
      try {
        // 获取图片的 Blob
        const response = await fetch(imageUrl);
        const blob = await response.blob();
        
        // 创建 File 对象
        const file = new File([blob], fileName, { type: blob.type });
        return file;
      } catch (error) {
        console.error('转换图片到 File 对象失败:', error);
        this.showErrorToast('转换图片到 File 对象失败: ' + error.message);
        return null;
      }
    },
    
    async getTestImageAsFile() {
      // 将导入的静态资源路径转换为 URL
      const imageUrl = new URL(testImg, import.meta.url).href;
      return await this.convertImageToFile(imageUrl);
    },

    startProgress() {
      this.progress = 0
      const loop = () => {
        this.progress = 0
        const interval = setInterval(() => {
          if (this.progress < 100) {
            this.progress += 1
          } else {
            clearInterval(interval)
            setTimeout(loop, 300) // 稍作停顿后重启循环
          }
        }, 50) // 每 50ms 增加 1%，约 5 秒一轮
      }
      loop()
    },

    onSwiperReady(swiperInstance) {
      this.fgSwiper = swiperInstance
      console.log('前景 Swiper 准备完成')
    },
    onBGSwiperReady(swiperInstance){
      swiperInstance.slideNext(600)
    },
    onBgSlideChange() {
      setTimeout(() => {
        if (this.fgSwiper) {
          this.fgSwiper.slideNext(600)
        }
      }, 500)
    }
  }
}
</script>

<style lang="less" scoped>
.bk{
  height: 100vh;
  width: 100vw;
  position: absolute;
  object-fit: cover;
  z-index: -1;
}
.title{
  // height: 200rem;
  width: 90vw;
  position:absolute;
  z-index: 555;
  margin-top: 100rem;
}
video{
  object-fit: contain;
  width: 100%;
  height: 100%;
}
.videobox{
  // height: 100%;
  height: 1600rem;
  width: 100%;
  position: relative;
  overflow: hidden;
  video{
   
    object-fit: cover;
    width: 100%;
    height: 100%;
  }
}
.home {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;

  .background-swiper {
    position: absolute;
    inset: 0;
    z-index: 0;

    .swiper-slide {
      width: 100%;
      height: 100%;

      .bg-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        filter: blur(3px) brightness(0.65);
      }
    }
  }

  .background-overlay {
    position: absolute;
    inset: 0;
    background-color: rgba(0.6, 0.6, 0.6, 0.2);
    z-index: 1;
  }
    .my-video-wraper {
    width: 100%;
    height: 100%;
    margin: auto;
    position: relative;
    video {
      object-fit: cover;
      width: 100%;
      height: 100%;
    }
  }

  .error-toast {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: #ff4d4f;
    color: white;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: bold;
    z-index: 9999;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    animation: fadeInDown 0.3s ease-out;
  }

  @keyframes fadeInDown {
    0% {
      opacity: 0;
      transform: translate(-50%, -20px);
    }
    100% {
      opacity: 1;
      transform: translate(-50%, 0);
    }
  }





  .carousel-container {
    flex: 5;
    z-index: 2;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: -10vw;
    flex-direction: column;

    .carousel-card {
      background: rgba(255, 255, 255, 0.05);
      backdrop-filter: blur(2px);
      border: 1px solid rgba(255, 255, 255, 0.4);
      padding: 12px;
      border-radius: 20px;
      box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
      width: 65vw;
      position: relative; // ✅ 保证内部绝对定位元素相对本容器定位

      .carousel-title {
        position: relative; // ✅ 让其在模糊层之上显示
        z-index: 2;
        text-align: center;
        font-size: 5vw;
        font-weight: bold;
        color:rgb(230,230,230); // ✅ 改为纯黑，更清晰
        margin-bottom: 10px;
        //background-color: rgba(255, 255, 255, 0.85); // ✅ 给文字加浅底背景
        padding: 8px 12px;
        border-radius: 12px;
        display: inline-block;
        margin-left: auto;
        margin-right: auto;
      }

      .carousel-image {
        width: 100%;
        border-radius: 10px;
        object-fit: cover;
        display: block;
      }
    }

  }

  .progress-bar-wrapper {
    width: 65vw;
    height: 12px;
    position: absolute;
    bottom: 150rem;
    // margin-top: 150rem;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    overflow: hidden;
    z-index: 2;

    .progress-bar-inner {
      height: 100%;
      background: linear-gradient(to right, #ff4d4f, #ff7875);
      transition: width 0.2s linear;
    }
  }
  .loading-text {
    position: absolute;
    bottom: 220rem;
    width: 100%;
    font-size: 90rem;
    text-align: center;
    letter-spacing: 14.5rem;
    color: #fff;
    font-weight:700;
  }


}
</style>
