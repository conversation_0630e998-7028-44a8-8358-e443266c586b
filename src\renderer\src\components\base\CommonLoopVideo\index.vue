<template>
  <video
    ref="player1"
    class="video-player"
    :style="{ display: isShowVideo1 ? 'unset' : 'none' }"
    :src="props.videoSrc"
    autoplay
    playsinline
    muted
    preload="auto"
    @ended="switchPlayer"
  ></video>
  <video
    ref="player2"
    class="video-player"
    :style="{ display: !isShowVideo1 ? 'unset' : 'none' }"
    :src="props.videoSrc"
    autoplay
    playsinline
    preload="auto"
    muted
    @ended="switchPlayer"
  ></video>
</template>

<script setup>
import { ref, nextTick } from 'vue'

const props = defineProps({
  videoSrc: {
    type: String,
    required: true
  }
})

const isShowVideo1 = ref(true)
const player1 = ref()
const player2 = ref()
const switchPlayer = () => {
  isShowVideo1.value = !isShowVideo1.value
  nextTick(() => {
    if (isShowVideo1.value) {
      player1.value.src = props.videoSrc
      player1.value.play()
      player2.value.pause()
      player2.value.src = ''
    } else {
      player2.value.src = props.videoSrc
      player2.value.play()
      player1.value.pause()
      player1.value.src = ''
    }
  })
}
</script>

<style scoped>
.video-player {
  position: absolute; /* 脱离文档流 */
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  will-change: transform; /* 提示浏览器优化 */
}
</style>
