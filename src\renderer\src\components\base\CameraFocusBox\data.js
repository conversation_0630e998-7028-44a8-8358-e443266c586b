const funArr = [
    {
      id: 1,
      position: ['top', 'left'],
      fuZhiIndex: 1,
      isNeedCompute: true,
      isNeedTransform: true
    },
    {
      id: 2,
      position: ['top', 'left'],
      fuZhiIndex: 1,
      isNeedCompute: false,
      isNeedTransform: false
    },
    {
      id: 3,
      position: ['top', 'right'],
      fuZhiIndex: 1,
      isNeedCompute: false,
      isNeedTransform: false
    },
    {
      id: 4,
      position: ['top', 'right'],
      fuZhiIndex: 1,
      isNeedCompute: true,
      isNeedTransform: true
    },
    {
      id: 5,
      position: ['bottom', 'left'],
      fuZhiIndex: 1,
      isNeedCompute: true,
      isNeedTransform: true
    },
    {
      id: 6,
      position: ['bottom', 'left'],
      fuZhiIndex: 1,
      isNeedCompute: false,
      isNeedTransform: false
    },
    {
      id: 7,
      position: ['bottom', 'right'],
      fuZhiIndex: 1,
      isNeedCompute: false,
      isNeedTransform: false
    },
    {
      id: 8,
      position: ['bottom', 'right'],
      fuZhiIndex: 1,
      isNeedCompute: true,
      isNeedTransform: true
    }
]
  const hengArr = [
    {
        position: ['top', 'left'],
        transform: 'translate(-50%, -50%)'
      },
      {
        position: ['right', 'top'],
        transform: 'translate(50%, -50%) rotate(90deg)'
      },
      {
        position: ['bottom', 'left'],
        transform: 'translate(-50%, 50%)'
      },
      {
        position: ['left', 'bottom'],
        transform: 'translate(-50%, 50%) rotate(-90deg)'
      },
      // 新增十字准星线
    //   {
    //     position: ['top', 'left'],
    //     transform: 'translate(-100%, -50%) rotate(90deg)'
    //   },
    //   {
    //     position: ['top', 'right'],
    //     transform: 'translate(100%, -50%) rotate(90deg)'
    //   }
  ]

  export {
    funArr,
    hengArr
  }